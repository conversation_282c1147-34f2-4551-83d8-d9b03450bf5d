<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\RestaurantInfo;

class UpdateRestaurantInfoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // ลบข้อมูลเก่าทั้งหมด
        RestaurantInfo::truncate();
        
        // สร้างข้อมูลใหม่
        RestaurantInfo::create([
            'name' => 'ร้านก๋วยเตี๋ยวเรือเข้าท่า',
            'description' => 'ก๋วยเตี๋ยวเรือต้นตำรับ รสชาติดั้งเดิม ด้วยสูตรลับเฉพาะตัว เครื่องเทศครบรส',
            'tagline' => 'ลิ้มลองรสชาติความอร่อยระดับตำนาน ที่ยังคงไว้ซึ่งเอกลักษณ์ไทย',
            'address' => 'ถนนเพชรเจริญ, Phetchabun, Thailand, Phetchabun',
            'phone' => '02-123-4567',
            'mobile' => '************',
            'email' => '<EMAIL>',
            'website' => 'https://lastnoodle.com',
            'facebook' => 'https://facebook.com/lastnoodle',
            'line' => '@lastnoodle',
            'instagram' => '@lastnoodle_official',
            'open_time' => '08:00',
            'close_time' => '20:00',
            'open_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
            'latitude' => 13.7563,
            'longitude' => 100.5018,
            'is_active' => true,
        ]);
        
        echo "อัปเดตข้อมูลร้านเรียบร้อยแล้ว\n";
    }
}
