<?php

require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\RestaurantInfo;

echo "=== ตรวจสอบข้อมูลร้านแบบครบถ้วน ===\n";

// ตรวจสอบข้อมูลปัจจุบัน
$info = RestaurantInfo::first();

if ($info) {
    echo "ข้อมูลปัจจุบัน:\n";
    echo "- ID: " . $info->id . "\n";
    echo "- ชื่อร้าน: " . ($info->name ?? 'ไม่มี') . "\n";
    echo "- คำอธิบาย: " . ($info->description ?? 'ไม่มี') . "\n";
    echo "- สโลแกน: " . ($info->tagline ?? 'ไม่มี') . "\n";
    echo "- ที่อยู่: " . ($info->address ?? 'ไม่มี') . "\n";
    echo "- เบอร์โทรศัพท์: " . ($info->phone ?? 'ไม่มี') . "\n";
    echo "- เบอร์มือถือ: " . ($info->mobile ?? 'ไม่มี') . "\n";
    echo "- อีเมล: " . ($info->email ?? 'ไม่มี') . "\n";
    echo "- เว็บไซต์: " . ($info->website ?? 'ไม่มี') . "\n";
    echo "- Facebook: " . ($info->facebook ?? 'ไม่มี') . "\n";
    echo "- Line: " . ($info->line ?? 'ไม่มี') . "\n";
    echo "- Instagram: " . ($info->instagram ?? 'ไม่มี') . "\n";
    echo "- เวลาเปิด: " . ($info->open_time ?? 'ไม่มี') . "\n";
    echo "- เวลาปิด: " . ($info->close_time ?? 'ไม่มี') . "\n";
    echo "- วันเปิดทำการ: " . (is_array($info->open_days) ? implode(', ', $info->open_days) : ($info->open_days ?? 'ไม่มี')) . "\n";
    echo "- สถานะ: " . ($info->is_active ? 'เปิดใช้งาน' : 'ปิดใช้งาน') . "\n";
    echo "- วันที่สร้าง: " . $info->created_at . "\n";
    echo "- วันที่แก้ไขล่าสุด: " . $info->updated_at . "\n";
    
} else {
    echo "ไม่พบข้อมูลร้าน\n";
}

echo "\n=== ทดสอบ getInfo() method ===\n";
$getInfo = RestaurantInfo::getInfo();
if ($getInfo) {
    echo "ชื่อร้านจาก getInfo(): " . ($getInfo->name ?? 'ไม่มี') . "\n";
    echo "เบอร์โทรศัพท์จาก getInfo(): " . ($getInfo->phone ?? 'ไม่มี') . "\n";
    echo "เบอร์มือถือจาก getInfo(): " . ($getInfo->mobile ?? 'ไม่มี') . "\n";
}

echo "\n=== เสร็จสิ้น ===\n";
