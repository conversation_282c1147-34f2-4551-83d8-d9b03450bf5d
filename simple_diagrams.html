<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Diagrams - LastNoodle Restaurant System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 40px;
            margin-bottom: 20px;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 5px;
        }
        .error-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .error-box h3 {
            color: #856404;
            margin-top: 0;
        }
        .solution-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .solution-box h4 {
            color: #155724;
            margin-top: 0;
        }
        .code-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .step {
            background: #e3f2fd;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
            border-radius: 0 5px 5px 0;
        }
        .step strong {
            color: #1976d2;
        }
        .diagram-text {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-line;
            border: 2px dashed #ccc;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #cce5ff;
            color: #004085;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #99d6ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 การแก้ปัญหาการเปิดไฟล์ Mermaid Diagrams</h1>
        
        <div class="error-box">
            <h3>⚠️ ปัญหาที่พบ</h3>
            <p>ไฟล์ <code>mermaid_to_images.html</code> เปิดไม่ได้ อาจเกิดจาก:</p>
            <ul>
                <li>ไม่มีการเชื่อมต่ออินเทอร์เน็ต (ต้องใช้โหลด Mermaid library)</li>
                <li>เบราว์เซอร์บล็อกการโหลดไฟล์ local</li>
                <li>ไฟล์เสียหายหรือไม่สมบูรณ์</li>
            </ul>
        </div>

        <h2>🛠️ วิธีแก้ปัญหา</h2>

        <div class="solution-box">
            <h4>วิธีที่ 1: เปิดด้วยเบราว์เซอร์โดยตรง</h4>
            <div class="step">
                <strong>ขั้นที่ 1:</strong> คลิกขวาที่ไฟล์ <code>mermaid_to_images.html</code>
            </div>
            <div class="step">
                <strong>ขั้นที่ 2:</strong> เลือก "Open with" → เลือกเบราว์เซอร์ (Chrome, Edge, Firefox)
            </div>
            <div class="step">
                <strong>ขั้นที่ 3:</strong> รอให้ไดอะแกรมโหลด (ต้องมีอินเทอร์เน็ต)
            </div>
        </div>

        <div class="solution-box">
            <h4>วิธีที่ 2: ลากไฟล์ใส่เบราว์เซอร์</h4>
            <div class="step">
                <strong>ขั้นที่ 1:</strong> เปิดเบราว์เซอร์ (Chrome แนะนำ)
            </div>
            <div class="step">
                <strong>ขั้นที่ 2:</strong> ลากไฟล์ <code>mermaid_to_images.html</code> ใส่หน้าต่างเบราว์เซอร์
            </div>
            <div class="step">
                <strong>ขั้นที่ 3:</strong> ปล่อยไฟล์และรอให้โหลด
            </div>
        </div>

        <div class="solution-box">
            <h4>วิธีที่ 3: ใช้ Address Bar</h4>
            <div class="step">
                <strong>ขั้นที่ 1:</strong> เปิดเบราว์เซอร์
            </div>
            <div class="step">
                <strong>ขั้นที่ 2:</strong> กด Ctrl + L หรือคลิกที่ Address Bar
            </div>
            <div class="step">
                <strong>ขั้นที่ 3:</strong> พิมพ์ที่อยู่ไฟล์:
                <div class="code-box">file:///C:/xampp/htdocs/LastNoodletest/mermaid_to_images.html</div>
            </div>
        </div>

        <h2>📊 ตัวอย่างไดอะแกรมที่ควรจะเห็น</h2>

        <div class="info">
            <strong>หมายเหตุ:</strong> นี่คือตัวอย่างโครงสร้างไดอะแกรมที่ควรจะแสดงในไฟล์ HTML
        </div>

        <h3>1. 🏗️ โครงสร้างฐานข้อมูล (ERD)</h3>
        <div class="diagram-text">
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     admin       │    │   categories    │    │   menu_items    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │    │ id (PK)         │
│ name            │    │ name            │    │ category_id (FK)│
│ email (UK)      │    │ slug (UK)       │    │ name            │
│ password        │    │ description     │    │ description     │
│ role            │    │ icon            │    │ price           │
│ created_at      │    │ image           │    │ image           │
│ updated_at      │    │ sort_order      │    │ is_featured     │
└─────────────────┘    │ is_active       │    │ is_active       │
         │              │ created_at      │    │ sort_order      │
         │              │ updated_at      │    │ created_at      │
         │              └─────────────────┘    │ updated_at      │
         │                       │             └─────────────────┘
         │                       │                      │
         │                       └──────────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      news       │    │ restaurant_info │    │    sessions     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │    │ id (PK)         │
│ title           │    │ name            │    │ user_id (FK)    │
│ content         │    │ description     │    │ ip_address      │
│ excerpt         │    │ tagline         │    │ user_agent      │
│ image           │    │ address         │    │ payload         │
│ is_published    │    │ phone           │    │ last_activity   │
│ is_featured     │    │ mobile          │    └─────────────────┘
│ sort_order      │    │ email           │
│ published_at    │    │ website         │
│ created_by (FK) │    │ facebook        │
│ created_at      │    │ line            │
│ updated_at      │    │ instagram       │
└─────────────────┘    │ open_time       │
                       │ close_time      │
                       │ open_days       │
                       │ logo            │
                       │ cover_image     │
                       │ background_image│
                       │ map_embed       │
                       │ latitude        │
                       │ longitude       │
                       │ is_active       │
                       │ created_at      │
                       │ updated_at      │
                       └─────────────────┘
        </div>

        <h3>2. 🏛️ สถาปัตยกรรมระบบ</h3>
        <div class="diagram-text">
Frontend Layer
├── Public Website
├── Admin Panel
└── Authentication

Controller Layer
├── Public Controllers
│   ├── HomeController
│   ├── MenuController
│   ├── NewsController
│   └── ContactController
└── Admin Controllers
    ├── AuthController
    ├── CategoryController
    ├── MenuItemController
    ├── Admin NewsController
    └── RestaurantInfoController

Model Layer
├── User Model
├── Category Model
├── MenuItem Model
├── News Model
└── RestaurantInfo Model

Database Layer
├── User Tables
│   ├── admin
│   └── sessions
├── Menu Tables
│   ├── categories
│   └── menu_items
└── Content Tables
    ├── news
    ├── restaurant_info
    └── migrations

Static Data
├── About Page Data
└── Contact Page Data
        </div>

        <div class="success">
            <strong>✅ หากเห็นไดอะแกรมแบบกราฟิกในไฟล์ HTML:</strong>
            <ul>
                <li>คลิกขวาบนไดอะแกรม → "Save image as..."</li>
                <li>เลือกรูปแบบ PNG หรือ SVG</li>
                <li>ตั้งชื่อไฟล์และบันทึก</li>
            </ul>
        </div>

        <div class="warning">
            <strong>⚠️ หากยังเปิดไม่ได้:</strong>
            <ul>
                <li>ตรวจสอบการเชื่อมต่ออินเทอร์เน็ต</li>
                <li>ลองเบราว์เซอร์อื่น (Chrome แนะนำ)</li>
                <li>ปิด Antivirus หรือ Firewall ชั่วคราว</li>
                <li>ใช้ไฟล์ DATA_DICTIONARY_WITH_DIAGRAMS.md แทน</li>
            </ul>
        </div>

        <h2>📱 ทางเลือกอื่น</h2>
        
        <div class="info">
            <strong>หากไฟล์ HTML ยังเปิดไม่ได้:</strong>
            <br>คุณสามารถใช้ไฟล์ <code>DATA_DICTIONARY_WITH_DIAGRAMS.md</code> 
            ที่มีโค้ด Mermaid อยู่แล้ว และคัดลอกไปใช้ในเครื่องมือออนไลน์เช่น:
            <ul>
                <li><strong>Mermaid Live Editor:</strong> https://mermaid.live/</li>
                <li><strong>GitHub:</strong> สร้าง README.md ใหม่และวางโค้ด Mermaid</li>
                <li><strong>VS Code:</strong> ติดตั้ง Mermaid Preview extension</li>
            </ul>
        </div>
    </div>
</body>
</html>
