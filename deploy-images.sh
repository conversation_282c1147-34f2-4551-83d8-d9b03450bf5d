#!/bin/bash

echo "========================================"
echo "  LastNoodle - Image Deployment Script"
echo "========================================"
echo

echo "[1/4] Creating storage link..."
php artisan storage:link --force
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to create storage link"
    exit 1
fi
echo "✓ Storage link created successfully"
echo

echo "[2/4] Checking image storage configuration..."
php artisan images:check --fix
if [ $? -ne 0 ]; then
    echo "ERROR: Image storage check failed"
    exit 1
fi
echo "✓ Image storage configuration checked"
echo

echo "[3/4] Setting permissions (Linux/Mac only)..."
if [[ "$OSTYPE" != "msys" && "$OSTYPE" != "win32" ]]; then
    chmod -R 755 storage
    chmod -R 775 storage/app/public
    echo "✓ Permissions set"
else
    echo "✓ Skipped (Windows detected)"
fi
echo

echo "[4/4] Clearing cache..."
php artisan cache:clear
php artisan config:clear
php artisan view:clear
echo "✓ Cache cleared"
echo

echo "[5/5] Final verification..."
php artisan images:check
if [ $? -ne 0 ]; then
    echo "WARNING: Some issues detected, but deployment completed"
else
    echo "✓ All checks passed!"
fi
echo

echo "========================================"
echo "  Deployment completed successfully!"
echo "========================================"
echo
echo "Next steps:"
echo "1. Open your website in browser"
echo "2. Check if images are displaying correctly"
echo "3. Test image upload in admin panel"
echo
echo "If you encounter any issues, check:"
echo "- IMAGE_DEPLOYMENT_GUIDE.md"
echo "- IMAGES_README.md"
echo
