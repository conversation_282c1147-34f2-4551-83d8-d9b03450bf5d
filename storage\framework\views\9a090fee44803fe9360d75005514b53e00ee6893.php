<?php $__env->startSection('title', $news->title . ' - ร้านก๋วยเตี๋ยวเรือเข้าท่า'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-decoration-none">หน้าหลัก</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('news.index')); ?>" class="text-decoration-none">ข่าวสาร</a></li>
            <li class="breadcrumb-item active" aria-current="page"><?php echo e($news->title); ?></li>
        </ol>
    </nav>

    <div class="row">
        <!-- News Content -->
        <div class="col-lg-8">
            <article class="card border-0 shadow-lg">
                <?php if($news->image): ?>
                    <img src="<?php echo e(asset('storage/' . $news->image)); ?>" 
                         class="card-img-top" 
                         alt="<?php echo e($news->title); ?>"
                         style="height: 400px; object-fit: cover;">
                <?php endif; ?>
                
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h1 class="card-title h2 mb-0"><?php echo e($news->title); ?></h1>
                        <?php if($news->is_featured): ?>
                            <span class="badge bg-warning text-dark fs-6 px-3 py-2">
                                <i class="fas fa-star me-1"></i>ข่าวเด่น
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="text-muted mb-4">
                        <i class="fas fa-calendar-alt me-2"></i>
                        <?php echo e($news->created_at->format('d/m/Y H:i')); ?>

                        <?php if($news->created_at != $news->updated_at): ?>
                            <span class="ms-3">
                                <i class="fas fa-edit me-2"></i>
                                แก้ไขล่าสุด: <?php echo e($news->updated_at->format('d/m/Y H:i')); ?>

                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <?php if($news->excerpt): ?>
                        <div class="alert alert-info border-0 mb-4">
                            <h5 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>สรุป
                            </h5>
                            <p class="mb-0"><?php echo e($news->excerpt); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="news-content">
                        <?php echo nl2br(e($news->content)); ?>

                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="d-flex gap-3">
                        <a href="<?php echo e(route('news.index')); ?>"
                           class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>กลับไปดูข่าวสารทั้งหมด
                        </a>
                    </div>
                </div>
            </article>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- News Info -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>ข้อมูลข่าว
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>วันที่เผยแพร่:</strong><br>
                        <span class="text-muted"><?php echo e($news->created_at->format('d/m/Y H:i')); ?></span>
                    </div>
                    <?php if($news->created_at != $news->updated_at): ?>
                        <div class="mb-3">
                            <strong>แก้ไขล่าสุด:</strong><br>
                            <span class="text-muted"><?php echo e($news->updated_at->format('d/m/Y H:i')); ?></span>
                        </div>
                    <?php endif; ?>
                    <div>
                        <strong>สถานะ:</strong><br>
                        <span class="badge bg-success">เผยแพร่แล้ว</span>
                        <?php if($news->is_featured): ?>
                            <span class="badge bg-warning text-dark ms-1">ข่าวเด่น</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Related News -->
            <?php if($relatedNews->count() > 0): ?>
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-newspaper me-2"></i>ข่าวสารอื่นๆ
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php $__currentLoopData = $relatedNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="p-3 border-bottom">
                                <?php if($item->image): ?>
                                    <img src="<?php echo e(asset('storage/' . $item->image)); ?>" 
                                         class="w-100 rounded mb-2" 
                                         alt="<?php echo e($item->title); ?>"
                                         style="height: 120px; object-fit: cover;">
                                <?php endif; ?>
                                
                                <h6 class="mb-2">
                                    <a href="<?php echo e(route('news.show', $item->id)); ?>" 
                                       class="text-decoration-none text-dark">
                                        <?php echo e($item->title); ?>

                                    </a>
                                </h6>
                                
                                <?php if($item->excerpt): ?>
                                    <p class="text-muted small mb-2"><?php echo e(Str::limit($item->excerpt, 80)); ?></p>
                                <?php endif; ?>
                                
                                <small class="text-muted">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    <?php echo e($item->created_at->format('d/m/Y')); ?>

                                </small>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\LastNoodletest\resources\views/news/show.blade.php ENDPATH**/ ?>