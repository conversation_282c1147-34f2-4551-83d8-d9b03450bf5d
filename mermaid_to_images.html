<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid Diagrams - LastNoodle Restaurant System</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 40px;
            margin-bottom: 20px;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 5px;
        }
        .diagram-container {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            position: relative;
        }
        .download-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }
        .download-btn:hover {
            background: #2980b9;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
        }
        .description {
            background: #e8f4fd;
            padding: 15px;
            border-left: 4px solid #3498db;
            margin-bottom: 20px;
            border-radius: 0 5px 5px 0;
        }
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .instructions h3 {
            color: #856404;
            margin-top: 0;
        }
        .instructions ol {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Mermaid Diagrams - ระบบร้านก๋วยเตี๋ยวเรือเข้าท่า</h1>
        
        <div class="instructions">
            <h3>🖼️ วิธีการบันทึกเป็นภาพ:</h3>
            <ol>
                <li><strong>คลิกขวา</strong>บนไดอะแกรมที่ต้องการ</li>
                <li>เลือก <strong>"Save image as..."</strong> หรือ <strong>"บันทึกภาพเป็น..."</strong></li>
                <li>เลือกรูปแบบไฟล์ <strong>PNG</strong> หรือ <strong>SVG</strong></li>
                <li>ตั้งชื่อไฟล์และบันทึก</li>
            </ol>
            <p><strong>หมายเหตุ:</strong> ไฟล์ SVG จะให้คุณภาพที่ดีที่สุดและสามารถขยายได้โดยไม่เสียคุณภาพ</p>
        </div>

        <!-- ERD Diagram -->
        <h2>🏗️ โครงสร้างฐานข้อมูล (Entity Relationship Diagram)</h2>
        <div class="description">
            <strong>คำอธิบาย:</strong> แสดงโครงสร้างตารางทั้งหมด 7 ตาราง พร้อมฟิลด์, ประเภทข้อมูล และความสัมพันธ์ระหว่างตาราง
        </div>
        <div class="diagram-container">
            <button class="download-btn" onclick="downloadDiagram('erd-diagram', 'database-structure')">💾 Download</button>
            <div class="mermaid" id="erd-diagram">
erDiagram
    %% User Management System
    admin {
        BIGINT id PK "รหัสผู้ใช้งานอัตโนมัติ"
        VARCHAR name "ชื่อผู้ใช้งาน"
        VARCHAR email UK "อีเมลผู้ใช้งาน (ไม่ซ้ำ)"
        TIMESTAMP email_verified_at "วันที่ยืนยันอีเมล"
        VARCHAR password "รหัสผ่าน (เข้ารหัส)"
        VARCHAR role "บทบาท (admin, user)"
        VARCHAR remember_token "Token จำรหัสผ่าน"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    sessions {
        VARCHAR id PK "รหัสเซสชัน"
        BIGINT user_id FK "รหัสผู้ใช้งาน"
        VARCHAR ip_address "IP Address"
        TEXT user_agent "User Agent"
        LONGTEXT payload "ข้อมูลเซสชัน"
        INT last_activity "กิจกรรมล่าสุด (timestamp)"
    }

    %% Menu Management System
    categories {
        BIGINT id PK "รหัสหมวดหมู่อัตโนมัติ"
        VARCHAR name "ชื่อหมวดหมู่"
        VARCHAR slug UK "URL Slug (ไม่ซ้ำ)"
        TEXT description "รายละเอียดหมวดหมู่"
        VARCHAR icon "ไอคอน FontAwesome"
        VARCHAR image "รูปภาพหมวดหมู่"
        INT sort_order "ลำดับการแสดง"
        BOOLEAN is_active "สถานะการใช้งาน"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    menu_items {
        BIGINT id PK "รหัสเมนูอัตโนมัติ"
        BIGINT category_id FK "รหัสหมวดหมู่"
        VARCHAR name "ชื่อเมนู"
        TEXT description "รายละเอียดเมนู"
        DECIMAL price "ราคา (บาท)"
        VARCHAR image "รูปภาพเมนู"
        BOOLEAN is_featured "เมนูแนะนำ"
        BOOLEAN is_active "สถานะการใช้งาน"
        INT sort_order "ลำดับการแสดง"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    %% Content Management System
    news {
        BIGINT id PK "รหัสข่าวอัตโนมัติ"
        VARCHAR title "หัวข้อข่าว"
        TEXT content "เนื้อหาข่าว"
        TEXT excerpt "สรุปข่าว"
        VARCHAR image "รูปภาพประกอบ"
        BOOLEAN is_published "สถานะเผยแพร่"
        BOOLEAN is_featured "ข่าวแนะนำ"
        INT sort_order "ลำดับการแสดง"
        TIMESTAMP published_at "วันที่เผยแพร่"
        BIGINT created_by FK "ผู้สร้าง"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    %% Restaurant Information System
    restaurant_info {
        BIGINT id PK "รหัสข้อมูลร้าน"
        VARCHAR name "ชื่อร้าน"
        TEXT description "คำอธิบายร้าน"
        TEXT tagline "สโลแกนร้าน"
        VARCHAR address "ที่อยู่"
        VARCHAR phone "เบอร์โทรศัพท์"
        VARCHAR mobile "เบอร์มือถือ"
        VARCHAR email "อีเมล"
        VARCHAR website "เว็บไซต์"
        VARCHAR facebook "Facebook URL"
        VARCHAR line "Line ID"
        VARCHAR instagram "Instagram URL"
        TIME open_time "เวลาเปิด"
        TIME close_time "เวลาปิด"
        JSON open_days "วันที่เปิด (array)"
        VARCHAR logo "โลโก้ร้าน"
        VARCHAR cover_image "รูปปกร้าน"
        VARCHAR background_image "รูปพื้นหลัง"
        TEXT map_embed "Google Maps embed code"
        DECIMAL latitude "ละติจูด"
        DECIMAL longitude "ลองจิจูด"
        BOOLEAN is_active "สถานะการใช้งาน"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    %% System Table
    migrations {
        INT id PK "รหัส migration อัตโนมัติ"
        VARCHAR migration "ชื่อไฟล์ migration"
        INT batch "รอบการรัน migration"
    }

    %% Relationships (Clean and Organized)
    admin ||--o{ news : "creates (created_by)"
    admin ||--o{ sessions : "has (user_id)"
    categories ||--o{ menu_items : "contains (category_id)"
            </div>
        </div>

        <!-- System Architecture -->
        <h2>🏛️ สถาปัตยกรรมระบบ (System Architecture)</h2>
        <div class="description">
            <strong>คำอธิบาย:</strong> แสดงโครงสร้างระบบแบบ Layer จาก Frontend ถึง Database พร้อมการเชื่อมต่อระหว่าง Components
        </div>
        <div class="diagram-container">
            <button class="download-btn" onclick="downloadDiagram('architecture-diagram', 'system-architecture')">💾 Download</button>
            <div class="mermaid" id="architecture-diagram">
graph TD
    %% Frontend Layer
    subgraph FL["🌐 Frontend Layer"]
        direction LR
        PW[Public Website]
        AP[Admin Panel]
        AU[Authentication]
    end

    %% Controller Layer
    subgraph CL["🎮 Controller Layer"]
        direction TB
        subgraph PC["Public Controllers"]
            HC[HomeController]
            MC[MenuController]
            NC[NewsController]
            CC[ContactController]
        end
        subgraph AC["Admin Controllers"]
            AuthC[AuthController]
            CatC[CategoryController]
            MIC[MenuItemController]
            ANC[Admin NewsController]
            RIC[RestaurantInfoController]
        end
    end

    %% Model Layer
    subgraph ML["📊 Model Layer"]
        direction LR
        UM[User Model]
        CM[Category Model]
        MIM[MenuItem Model]
        NM[News Model]
        RIM[RestaurantInfo Model]
    end

    %% Database Layer
    subgraph DL["🗄️ Database Layer"]
        direction TB
        subgraph UT["User Tables"]
            ADM[(admin)]
            SES[(sessions)]
        end
        subgraph MT["Menu Tables"]
            CAT[(categories)]
            MIT[(menu_items)]
        end
        subgraph CT["Content Tables"]
            NEW[(news)]
            RES[(restaurant_info)]
            MIG[(migrations)]
        end
    end

    %% Static Data
    subgraph SD["📄 Static Data"]
        direction LR
        APD[About Page Data]
        CPD[Contact Page Data]
    end

    %% Vertical Connections (Clean Lines)
    PW -.-> HC
    AP -.-> AuthC
    AU -.-> AuthC
    
    HC -.-> UM
    HC -.-> RIM
    HC -.-> APD
    
    MC -.-> CM
    MC -.-> MIM
    
    NC -.-> NM
    
    CC -.-> RIM
    CC -.-> CPD
    
    CatC -.-> CM
    MIC -.-> MIM
    ANC -.-> NM
    RIC -.-> RIM
    
    UM -.-> ADM
    CM -.-> CAT
    MIM -.-> MIT
    NM -.-> NEW
    RIM -.-> RES

    %% Relationships
    ADM -.-> NEW
    ADM -.-> SES
    CAT -.-> MIT

    %% Styling
    classDef frontend fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef controller fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef model fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef database fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef static fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class PW,AP,AU frontend
    class HC,MC,NC,CC,AuthC,CatC,MIC,ANC,RIC controller
    class UM,CM,MIM,NM,RIM model
    class ADM,SES,CAT,MIT,NEW,RES,MIG database
    class APD,CPD static
            </div>
        </div>

        <!-- Database Cleanup Process -->
        <h2>🧹 การทำความสะอาดฐานข้อมูล (Database Cleanup Process)</h2>
        <div class="description">
            <strong>คำอธิบาย:</strong> แสดงกระบวนการวิเคราะห์และลบตารางที่ไม่ได้ใช้งานออกจากฐานข้อมูล
        </div>
        <div class="diagram-container">
            <button class="download-btn" onclick="downloadDiagram('cleanup-diagram', 'database-cleanup')">💾 Download</button>
            <div class="mermaid" id="cleanup-diagram">
flowchart TD
    %% Start
    START[🚀 เริ่มต้น: 16 ตาราง] --> ANALYZE{🔍 วิเคราะห์การใช้งาน}

    %% Analysis Results
    ANALYZE --> USED[✅ ตารางที่ใช้งาน<br/>7 ตาราง]
    ANALYZE --> UNUSED[❌ ตารางที่ไม่ใช้<br/>9 ตาราง]

    %% Used Tables (Vertical alignment)
    USED --> U1[admin<br/>ผู้ใช้งาน]
    USED --> U2[categories<br/>หมวดหมู่]
    USED --> U3[menu_items<br/>เมนูอาหาร]
    USED --> U4[news<br/>ข่าวสาร]
    USED --> U5[restaurant_info<br/>ข้อมูลร้าน]
    USED --> U6[sessions<br/>เซสชัน Laravel]
    USED --> U7[migrations<br/>ประวัติ migration]

    %% Unused Tables Categories
    UNUSED --> DELETED[🗑️ ลบแล้ว<br/>7 ตาราง]
    UNUSED --> SHOULD_DELETE[⚠️ ควรลบ<br/>2 ตาราง]

    %% Already Deleted Tables
    DELETED --> D1[hero_sliders]
    DELETED --> D2[images]
    DELETED --> D3[font_settings]
    DELETED --> D4[failed_jobs]
    DELETED --> D5[personal_access_tokens]
    DELETED --> D6[password_resets]
    DELETED --> D7[settings]

    %% Should Delete Tables
    SHOULD_DELETE --> SD1[about_pages]
    SHOULD_DELETE --> SD2[contact_pages]

    %% Analysis Questions
    SD1 --> Q1{มี Model?}
    SD2 --> Q2{มี Admin Routes?}

    Q1 --> A1[❌ ไม่มี Model]
    Q2 --> A2[❌ ไม่มี Admin Routes]

    A1 --> REASON[💡 ใช้ Static Data แทน]
    A2 --> REASON

    %% Action
    REASON --> ACTION[🛠️ คำสั่งลบ]

    ACTION --> CMD1[php artisan db:cleanup --force]
    ACTION --> CMD2[DROP TABLE about_pages]
    ACTION --> CMD3[DROP TABLE contact_pages]

    %% Result
    CMD1 --> RESULT[🎯 ผลลัพธ์<br/>7 ตารางที่ใช้งานจริง]
    CMD2 --> RESULT
    CMD3 --> RESULT

    RESULT --> SUCCESS[🎉 ฐานข้อมูลสะอาด]

    %% Styling
    classDef startNode fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    classDef usedNode fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    classDef unusedNode fill:#ffcdd2,stroke:#c62828,stroke-width:2px
    classDef deletedNode fill:#f8bbd9,stroke:#ad1457,stroke-width:2px
    classDef actionNode fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef resultNode fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    classDef questionNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px

    class START startNode
    class USED,U1,U2,U3,U4,U5,U6,U7 usedNode
    class UNUSED,SHOULD_DELETE,SD1,SD2 unusedNode
    class DELETED,D1,D2,D3,D4,D5,D6,D7 deletedNode
    class ACTION,CMD1,CMD2,CMD3 actionNode
    class RESULT,SUCCESS resultNode
    class ANALYZE,Q1,Q2 questionNode
            </div>
        </div>

        <!-- MVC Structure -->
        <h2>🎮 โครงสร้าง MVC (Model-View-Controller)</h2>
        <div class="description">
            <strong>คำอธิบาย:</strong> แสดงความสัมพันธ์ระหว่าง View, Controller, Model และ Database ในรูปแบบ MVC Pattern
        </div>
        <div class="diagram-container">
            <button class="download-btn" onclick="downloadDiagram('mvc-diagram', 'mvc-structure')">💾 Download</button>
            <div class="mermaid" id="mvc-diagram">
graph TD
    %% Views Layer
    subgraph VL["🖥️ Views Layer"]
        direction TB
        subgraph PV["Public Views"]
            V1[home.blade.php]
            V2[menu/index.blade.php]
            V3[news/index.blade.php]
            V4[about.blade.php]
            V5[contact/index.blade.php]
        end
        subgraph AV["Admin Views"]
            V6[admin/dashboard.blade.php]
            V7[admin/categories/index.blade.php]
            V8[admin/menu-items/index.blade.php]
            V9[admin/news/index.blade.php]
        end
    end

    %% Controllers Layer
    subgraph CL["🎮 Controllers Layer"]
        direction TB
        subgraph PC["Public Controllers"]
            C1[HomeController]
            C2[MenuController]
            C3[NewsController]
            C4[ContactController]
        end
        subgraph AC["Admin Controllers"]
            C5[AuthController]
            C6[CategoryController]
            C7[MenuItemController]
            C8[Admin NewsController]
            C9[RestaurantInfoController]
        end
    end

    %% Models Layer
    subgraph ML["📊 Models Layer"]
        direction LR
        M1[User Model]
        M2[Category Model]
        M3[MenuItem Model]
        M4[News Model]
        M5[RestaurantInfo Model]
    end

    %% Database Layer
    subgraph DL["🗄️ Database Layer"]
        direction TB
        subgraph UT["User & System"]
            T1[(admin)]
            T6[(sessions)]
            T7[(migrations)]
        end
        subgraph MT["Menu System"]
            T2[(categories)]
            T3[(menu_items)]
        end
        subgraph CT["Content System"]
            T4[(news)]
            T5[(restaurant_info)]
        end
    end

    %% Static Data
    subgraph SD["📄 Static Data"]
        direction LR
        S1[About Page Data]
        S2[Contact Page Data]
    end

    %% Clean Vertical Connections
    %% Views to Controllers
    V1 -.-> C1
    V2 -.-> C2
    V3 -.-> C3
    V4 -.-> C1
    V5 -.-> C4
    V6 -.-> C5
    V7 -.-> C6
    V8 -.-> C7
    V9 -.-> C8

    %% Controllers to Models/Static
    C1 -.-> M5
    C1 -.-> S1
    C2 -.-> M2
    C2 -.-> M3
    C3 -.-> M4
    C4 -.-> M5
    C4 -.-> S2
    C5 -.-> M1
    C6 -.-> M2
    C7 -.-> M3
    C8 -.-> M4
    C9 -.-> M5

    %% Models to Database
    M1 -.-> T1
    M2 -.-> T2
    M3 -.-> T3
    M4 -.-> T4
    M5 -.-> T5

    %% Database Relationships
    T1 -.-> T4
    T1 -.-> T6
    T2 -.-> T3

    %% Styling
    classDef view fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef controller fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef model fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef database fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef static fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class V1,V2,V3,V4,V5,V6,V7,V8,V9 view
    class C1,C2,C3,C4,C5,C6,C7,C8,C9 controller
    class M1,M2,M3,M4,M5 model
    class T1,T2,T3,T4,T5,T6,T7 database
    class S1,S2 static
            </div>
        </div>

        <!-- Data Flow -->
        <h2>📊 การไหลของข้อมูล (Data Flow)</h2>
        <div class="description">
            <strong>คำอธิบาย:</strong> แสดงการไหลของข้อมูลจาก User ผ่าน Routes, Controllers ไปยัง Models และ Database
        </div>
        <div class="diagram-container">
            <button class="download-btn" onclick="downloadDiagram('dataflow-diagram', 'data-flow')">💾 Download</button>
            <div class="mermaid" id="dataflow-diagram">
flowchart TD
    %% User Interactions
    subgraph UI["👤 User Interactions"]
        direction TB
        PU[Public User]
        AU[Admin User]
    end

    %% Frontend Routes
    subgraph FR["🌐 Frontend Routes"]
        direction TB
        subgraph PR["Public Routes"]
            HOME[/ - Home]
            MENU[/menu - Menu]
            NEWS[/news - News]
            ABOUT[/about - About]
            CONTACT[/contact - Contact]
        end
        subgraph AR["Admin Routes"]
            LOGIN[/login - Login]
            ADMIN[/admin - Dashboard]
            ACAT[/admin/categories]
            AMENU[/admin/menu-items]
            ANEWS[/admin/news]
            AINFO[/admin/restaurant-info]
        end
    end

    %% Controllers Processing
    subgraph CP["🎮 Controllers Processing"]
        direction TB
        subgraph PC["Public Controllers"]
            HC[HomeController]
            MC[MenuController]
            NC[NewsController]
            CC[ContactController]
        end
        subgraph AC["Admin Controllers"]
            AuthC[AuthController]
            CatC[CategoryController]
            MIC[MenuItemController]
            ANC[Admin NewsController]
            RIC[RestaurantInfoController]
        end
    end

    %% Data Sources
    subgraph DS["📊 Data Sources"]
        direction TB
        subgraph DB["Database Tables"]
            TADM[(admin)]
            TCAT[(categories)]
            TMENU[(menu_items)]
            TNEWS[(news)]
            TINFO[(restaurant_info)]
            TSES[(sessions)]
        end
        subgraph SD["Static Data"]
            SABOUT[About Page Data]
            SCONTACT[Contact Page Data]
        end
    end

    %% User Flow - Public
    PU --> HOME
    PU --> MENU
    PU --> NEWS
    PU --> ABOUT
    PU --> CONTACT

    %% User Flow - Admin
    AU --> LOGIN
    AU --> ADMIN
    AU --> ACAT
    AU --> AMENU
    AU --> ANEWS
    AU --> AINFO

    %% Route to Controller - Public
    HOME --> HC
    MENU --> MC
    NEWS --> NC
    ABOUT --> HC
    CONTACT --> CC

    %% Route to Controller - Admin
    LOGIN --> AuthC
    ADMIN --> AuthC
    ACAT --> CatC
    AMENU --> MIC
    ANEWS --> ANC
    AINFO --> RIC

    %% Controller to Data - Public
    HC --> TINFO
    HC --> SABOUT
    MC --> TCAT
    MC --> TMENU
    NC --> TNEWS
    CC --> TINFO
    CC --> SCONTACT

    %% Controller to Data - Admin
    AuthC --> TADM
    AuthC --> TSES
    CatC --> TCAT
    MIC --> TMENU
    ANC --> TNEWS
    RIC --> TINFO

    %% Database Relationships
    TADM -.-> TNEWS
    TADM -.-> TSES
    TCAT -.-> TMENU

    %% Styling
    classDef user fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef route fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef controller fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef database fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef static fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class PU,AU user
    class HOME,MENU,NEWS,ABOUT,CONTACT,LOGIN,ADMIN,ACAT,AMENU,ANEWS,AINFO route
    class HC,MC,NC,CC,AuthC,CatC,MIC,ANC,RIC controller
    class TADM,TCAT,TMENU,TNEWS,TINFO,TSES database
    class SABOUT,SCONTACT static
            </div>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            er: {
                useMaxWidth: true
            }
        });

        // Function to download diagram as image
        function downloadDiagram(diagramId, filename) {
            const svg = document.querySelector(`#${diagramId} svg`);
            if (!svg) {
                alert('ไดอะแกรมยังไม่โหลดเสร็จ กรุณารอสักครู่แล้วลองใหม่');
                return;
            }

            // Create canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Get SVG dimensions
            const svgRect = svg.getBoundingClientRect();
            canvas.width = svgRect.width * 2; // Higher resolution
            canvas.height = svgRect.height * 2;
            
            // Create image from SVG
            const img = new Image();
            const svgData = new XMLSerializer().serializeToString(svg);
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                // Download
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = `${filename}.png`;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    URL.revokeObjectURL(url);
                });
            };
            
            img.src = url;
        }

        // Add right-click context menu for easy saving
        document.addEventListener('contextmenu', function(e) {
            if (e.target.closest('.mermaid')) {
                // Allow default context menu for easy image saving
                return true;
            }
        });
    </script>
</body>
</html>
