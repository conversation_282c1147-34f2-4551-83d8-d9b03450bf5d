erDiagram
    %% User Management System
    admin {
        BIGINT id PK "รหัสผู้ใช้งานอัตโนมัติ"
        VARCHAR name "ชื่อผู้ใช้งาน"
        VARCHAR email UK "อีเมลผู้ใช้งาน (ไม่ซ้ำ)"
        TIMESTAMP email_verified_at "วันที่ยืนยันอีเมล"
        VARCHAR password "รหัสผ่าน (เข้ารหัส)"
        VARCHAR role "บทบาท (admin, user)"
        VARCHAR remember_token "Token จำรหัสผ่าน"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    sessions {
        VARCHAR id PK "รหัสเซสชัน"
        BIGINT user_id FK "รหัสผู้ใช้งาน"
        VARCHAR ip_address "IP Address"
        TEXT user_agent "User Agent"
        LONGTEXT payload "ข้อมูลเซสชัน"
        INT last_activity "กิจกรรมล่าสุด (timestamp)"
    }

    %% Menu Management System
    categories {
        BIGINT id PK "รหัสหมวดหมู่อัตโนมัติ"
        VARCHAR name "ชื่อหมวดหมู่"
        VARCHAR slug UK "URL Slug (ไม่ซ้ำ)"
        TEXT description "รายละเอียดหมวดหมู่"
        VARCHAR icon "ไอคอน FontAwesome"
        VARCHAR image "รูปภาพหมวดหมู่"
        INT sort_order "ลำดับการแสดง"
        BOOLEAN is_active "สถานะการใช้งาน"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    menu_items {
        BIGINT id PK "รหัสเมนูอัตโนมัติ"
        BIGINT category_id FK "รหัสหมวดหมู่"
        VARCHAR name "ชื่อเมนู"
        TEXT description "รายละเอียดเมนู"
        DECIMAL price "ราคา (บาท)"
        VARCHAR image "รูปภาพเมนู"
        BOOLEAN is_featured "เมนูแนะนำ"
        BOOLEAN is_active "สถานะการใช้งาน"
        INT sort_order "ลำดับการแสดง"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    %% Content Management System
    news {
        BIGINT id PK "รหัสข่าวอัตโนมัติ"
        VARCHAR title "หัวข้อข่าว"
        TEXT content "เนื้อหาข่าว"
        TEXT excerpt "สรุปข่าว"
        VARCHAR image "รูปภาพประกอบ"
        BOOLEAN is_published "สถานะเผยแพร่"
        BOOLEAN is_featured "ข่าวแนะนำ"
        INT sort_order "ลำดับการแสดง"
        TIMESTAMP published_at "วันที่เผยแพร่"
        BIGINT created_by FK "ผู้สร้าง"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    %% Restaurant Information System
    restaurant_info {
        BIGINT id PK "รหัสข้อมูลร้าน"
        VARCHAR name "ชื่อร้าน"
        TEXT description "คำอธิบายร้าน"
        TEXT tagline "สโลแกนร้าน"
        VARCHAR address "ที่อยู่"
        VARCHAR phone "เบอร์โทรศัพท์"
        VARCHAR mobile "เบอร์มือถือ"
        VARCHAR email "อีเมล"
        VARCHAR website "เว็บไซต์"
        VARCHAR facebook "Facebook URL"
        VARCHAR line "Line ID"
        VARCHAR instagram "Instagram URL"
        TIME open_time "เวลาเปิด"
        TIME close_time "เวลาปิด"
        JSON open_days "วันที่เปิด (array)"
        VARCHAR logo "โลโก้ร้าน"
        VARCHAR cover_image "รูปปกร้าน"
        VARCHAR background_image "รูปพื้นหลัง"
        TEXT map_embed "Google Maps embed code"
        DECIMAL latitude "ละติจูด"
        DECIMAL longitude "ลองจิจูด"
        BOOLEAN is_active "สถานะการใช้งาน"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    %% System Table
    migrations {
        INT id PK "รหัส migration อัตโนมัติ"
        VARCHAR migration "ชื่อไฟล์ migration"
        INT batch "รอบการรัน migration"
    }

    %% Relationships (Clean and Organized)
    admin ||--o{ news : "creates (created_by)"
    admin ||--o{ sessions : "has (user_id)"
    categories ||--o{ menu_items : "contains (category_id)"
