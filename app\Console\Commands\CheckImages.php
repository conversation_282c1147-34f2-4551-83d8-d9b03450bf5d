<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use App\Models\MenuItem;
use App\Models\Category;
use App\Models\RestaurantInfo;
use App\Helpers\ImageHelper;

class CheckImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:check {--fix : Fix broken image paths}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check image files and storage link status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Checking image system status...');
        $this->newLine();

        // Check storage link
        $this->checkStorageLink();
        $this->newLine();

        // Check directories
        $this->checkDirectories();
        $this->newLine();

        // Check menu item images
        $this->checkMenuItemImages();
        $this->newLine();

        // Check category images
        $this->checkCategoryImages();
        $this->newLine();

        // Check restaurant images
        $this->checkRestaurantImages();
        $this->newLine();

        $this->info('✅ Image check completed!');
    }

    private function checkStorageLink()
    {
        $this->info('📁 Storage Link Status:');
        
        $storageLinkPath = public_path('storage');
        $storageAppPublicPath = storage_path('app/public');

        if (!File::exists($storageLinkPath)) {
            $this->error('❌ Storage link does not exist');
            $this->line('   Run: php artisan storage:link');
            return;
        }

        if (!is_link($storageLinkPath) && !is_dir($storageLinkPath)) {
            $this->error('❌ Storage link is not a valid link or directory');
            return;
        }

        // Test if we can access files through the link
        $testDirectories = ['menu-items', 'categories', 'restaurant'];
        $workingDirs = 0;

        foreach ($testDirectories as $dir) {
            $linkPath = $storageLinkPath . DIRECTORY_SEPARATOR . $dir;
            $realPath = $storageAppPublicPath . DIRECTORY_SEPARATOR . $dir;

            if (File::exists($realPath)) {
                if (File::exists($linkPath)) {
                    $workingDirs++;
                    $this->line("   ✅ {$dir}/ - accessible");
                } else {
                    $this->line("   ❌ {$dir}/ - not accessible through link");
                }
            } else {
                $this->line("   ⚠️  {$dir}/ - directory doesn't exist");
            }
        }

        if ($workingDirs > 0) {
            $this->info('✅ Storage link is working');
        } else {
            $this->error('❌ Storage link is not working properly');
        }
    }

    private function checkDirectories()
    {
        $this->info('📂 Directory Structure:');
        
        $directories = [
            'storage/app/public/menu-items',
            'storage/app/public/categories', 
            'storage/app/public/restaurant',
            'storage/app/public/about-page',
            'storage/app/public/contact-page',
            'storage/app/public/gallery',
            'public/images/menu',
            'public/images/restaurant',
            'public/images/logo'
        ];

        foreach ($directories as $dir) {
            $fullPath = base_path($dir);
            if (File::exists($fullPath)) {
                $fileCount = count(File::files($fullPath));
                $this->line("   ✅ {$dir}/ ({$fileCount} files)");
            } else {
                $this->line("   ❌ {$dir}/ - missing");
            }
        }
    }

    private function checkMenuItemImages()
    {
        $this->info('🍜 Menu Item Images:');
        
        $menuItems = MenuItem::whereNotNull('image')->get();
        $total = $menuItems->count();
        $working = 0;
        $broken = 0;

        foreach ($menuItems as $item) {
            $imageUrl = ImageHelper::getMenuImageUrl($item->image);
            $isWorking = $this->testImageUrl($item->image);
            
            if ($isWorking) {
                $working++;
                $this->line("   ✅ {$item->name} - {$item->image}");
            } else {
                $broken++;
                $this->line("   ❌ {$item->name} - {$item->image} (broken)");
            }
        }

        $this->line("   📊 Total: {$total}, Working: {$working}, Broken: {$broken}");
    }

    private function checkCategoryImages()
    {
        $this->info('📋 Category Images:');
        
        $categories = Category::whereNotNull('image')->get();
        $total = $categories->count();
        $working = 0;
        $broken = 0;

        foreach ($categories as $category) {
            $imageUrl = ImageHelper::getCategoryImageUrl($category->image);
            $isWorking = $this->testImageUrl($category->image);
            
            if ($isWorking) {
                $working++;
                $this->line("   ✅ {$category->name} - {$category->image}");
            } else {
                $broken++;
                $this->line("   ❌ {$category->name} - {$category->image} (broken)");
            }
        }

        $this->line("   📊 Total: {$total}, Working: {$working}, Broken: {$broken}");
    }

    private function checkRestaurantImages()
    {
        $this->info('🏪 Restaurant Images:');
        
        $restaurantInfo = RestaurantInfo::first();
        if (!$restaurantInfo) {
            $this->line("   ⚠️  No restaurant info found");
            return;
        }

        $images = [
            'background_image' => $restaurantInfo->background_image,
            'logo' => $restaurantInfo->logo,
            'cover_image' => $restaurantInfo->cover_image
        ];

        foreach ($images as $type => $imagePath) {
            if ($imagePath) {
                $isWorking = $this->testImageUrl($imagePath);
                if ($isWorking) {
                    $this->line("   ✅ {$type} - {$imagePath}");
                } else {
                    $this->line("   ❌ {$type} - {$imagePath} (broken)");
                }
            } else {
                $this->line("   ⚠️  {$type} - not set");
            }
        }
    }

    private function testImageUrl($imagePath)
    {
        if (empty($imagePath)) {
            return false;
        }

        // Check if the image exists in storage
        if (Storage::disk('public')->exists($imagePath)) {
            return true;
        }

        // Check if the image exists in public directory (legacy images)
        $publicPath = public_path($imagePath);
        if (File::exists($publicPath)) {
            return true;
        }

        // Check if the image exists in public/images directory
        $publicImagesPath = public_path('images/' . $imagePath);
        if (File::exists($publicImagesPath)) {
            return true;
        }

        return false;
    }
}
