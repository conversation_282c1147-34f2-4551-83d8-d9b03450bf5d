<?php

function copyDirectory($source, $destination) {
    if (!is_dir($source)) {
        return false;
    }
    
    if (!is_dir($destination)) {
        mkdir($destination, 0755, true);
    }
    
    $files = scandir($source);
    $copied = 0;
    
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            $sourcePath = $source . DIRECTORY_SEPARATOR . $file;
            $destPath = $destination . DIRECTORY_SEPARATOR . $file;
            
            if (is_dir($sourcePath)) {
                $copied += copyDirectory($sourcePath, $destPath);
            } else {
                if (copy($sourcePath, $destPath)) {
                    echo "✅ คัดลอก: $file\n";
                    $copied++;
                } else {
                    echo "❌ ไม่สามารถคัดลอก: $file\n";
                }
            }
        }
    }
    
    return $copied;
}

echo "=== คัดลอกรูปภาพจาก Storage ไปยัง Public ===\n\n";

$storageBase = __DIR__ . DIRECTORY_SEPARATOR . 'storage' . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'public';
$publicBase = __DIR__ . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'images';

echo "จาก: $storageBase\n";
echo "ไปยัง: $publicBase\n\n";

if (!is_dir($storageBase)) {
    echo "❌ ไม่พบโฟลเดอร์ storage: $storageBase\n";
    exit(1);
}

// สร้างโฟลเดอร์ public/images
if (!is_dir($publicBase)) {
    if (mkdir($publicBase, 0755, true)) {
        echo "✅ สร้างโฟลเดอร์: $publicBase\n\n";
    } else {
        echo "❌ ไม่สามารถสร้างโฟลเดอร์: $publicBase\n";
        exit(1);
    }
}

$folders = ['menu-items', 'categories', 'restaurant', 'about-page', 'contact-page', 'gallery', 'news'];
$totalCopied = 0;

foreach ($folders as $folder) {
    $sourceFolder = $storageBase . DIRECTORY_SEPARATOR . $folder;
    $destFolder = $publicBase . DIRECTORY_SEPARATOR . $folder;
    
    echo "📁 ประมวลผล: $folder\n";
    
    if (is_dir($sourceFolder)) {
        $copied = copyDirectory($sourceFolder, $destFolder);
        $totalCopied += $copied;
        echo "   คัดลอกแล้ว: $copied ไฟล์\n\n";
    } else {
        echo "   ⚠️  ไม่พบโฟลเดอร์: $sourceFolder\n\n";
    }
}

// สร้างไฟล์ .htaccess
$htaccessPath = $publicBase . DIRECTORY_SEPARATOR . '.htaccess';
$htaccessContent = "# Allow access to images
<IfModule mod_rewrite.c>
    RewriteEngine On
</IfModule>

# Set proper MIME types
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
    AddType image/svg+xml .svg
</IfModule>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Cache images for 1 month
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpeg \"access plus 1 month\"
    ExpiresByType image/png \"access plus 1 month\"
    ExpiresByType image/gif \"access plus 1 month\"
    ExpiresByType image/webp \"access plus 1 month\"
    ExpiresByType image/svg+xml \"access plus 1 month\"
</IfModule>";

file_put_contents($htaccessPath, $htaccessContent);
echo "✅ สร้างไฟล์ .htaccess\n\n";

echo "=== สรุปผลการคัดลอก ===\n";
echo "📋 ไฟล์ทั้งหมดที่คัดลอก: $totalCopied ไฟล์\n";
echo "📁 โฟลเดอร์ปลายทาง: $publicBase\n";
echo "🎉 เสร็จสิ้น! ตอนนี้รูปภาพสามารถแสดงได้ในทุกเครื่อง PC\n";
echo "📝 หมายเหตุ: รูปภาพจะถูกเข้าถึงผ่าน URL แบบ /images/folder/filename.jpg\n";
