<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

echo "=== ตรวจสอบและสร้าง Admin User ===\n";

try {
    // ตรวจสอบว่ามี admin user หรือไม่
    $admin = User::where('email', '<EMAIL>')->first();
    
    if ($admin) {
        echo "✅ พบ Admin User แล้ว\n";
        echo "   📧 Email: {$admin->email}\n";
        echo "   👤 Name: {$admin->name}\n";
        echo "   🔑 Role: {$admin->role}\n";
        echo "   🆔 ID: {$admin->id}\n";
        
        // ตรวจสอบรหัสผ่าน
        if (Hash::check('admin123', $admin->password)) {
            echo "✅ รหัสผ่าน admin123 ถูกต้อง\n";
        } else {
            echo "❌ รหัสผ่าน admin123 ไม่ถูกต้อง - กำลังอัปเดต...\n";
            $admin->password = Hash::make('admin123');
            $admin->save();
            echo "✅ อัปเดตรหัสผ่านเรียบร้อยแล้ว\n";
        }
        
    } else {
        echo "❌ ไม่พบ Admin User - กำลังสร้างใหม่...\n";
        
        $admin = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);
        
        echo "✅ สร้าง Admin User เรียบร้อยแล้ว\n";
        echo "   📧 Email: {$admin->email}\n";
        echo "   👤 Name: {$admin->name}\n";
        echo "   🔑 Role: {$admin->role}\n";
        echo "   🆔 ID: {$admin->id}\n";
    }
    
    echo "\n=== ข้อมูลสำหรับเข้าสู่ระบบ ===\n";
    echo "📧 Email: <EMAIL>\n";
    echo "🔑 Password: admin123\n";
    echo "🌐 URL: http://localhost:8000/login\n";
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
}
