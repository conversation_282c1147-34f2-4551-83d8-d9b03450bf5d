<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\News;
use App\Models\User;

// Get admin user
$admin = User::where('email', '<EMAIL>')->first();

if (!$admin) {
    echo "Admin user not found!\n";
    exit;
}

// Check if news already exists
$existingNews = News::count();
echo "Existing news count: $existingNews\n";

if ($existingNews < 5) {
    // Create sample news
    $news1 = News::create([
        'title' => 'ยินดีต้อนรับสู่ร้านก๋วยเตี๋ยวเรือเข้าท่า',
        'content' => 'ร้านก๋วยเตี๋ยวเรือเข้าท่า ขอต้อนรับทุกท่านสู่ร้านอาหารที่มีประวัติศาสตร์อันยาวนาน เรามีเมนูก๋วยเตี๋ยวเรือรสเด็ด พร้อมเครื่องเคียงมากมาย ที่จะทำให้ทุกท่านได้สัมผัสกับรสชาติดั้งเดิมของก๋วยเตี๋ยวเรือแท้ๆ

เรามีบริการทุกวัน ตั้งแต่เวลา 08:00 - 20:00 น. พร้อมให้บริการด้วยใจจริง

สามารถสั่งอาหารล่วงหน้าได้ หรือมาทานที่ร้านได้เลย

ขอบคุณทุกท่านที่ให้การสนับสนุน',
        'excerpt' => 'ยินดีต้อนรับสู่ร้านก๋วยเตี๋ยวเรือเข้าท่า ร้านอาหารที่มีประวัติศาสตร์อันยาวนาน',
        'is_published' => true,
        'is_featured' => true,
        'sort_order' => 1,
        'published_at' => now(),
        'created_by' => $admin->id,
    ]);

    $news2 = News::create([
        'title' => 'เมนูใหม่! ก๋วยเตี๋ยวเรือพิเศษ',
        'content' => 'ขอแนะนำเมนูใหม่ของทางร้าน "ก๋วยเตี๋ยวเรือพิเศษ" ที่มาพร้อมกับเครื่องเคียงมากมาย

- หมูแดง
- ลูกชิ้นปลา
- ลูกชิ้นหมู
- เต้าหู้ทอด
- ผักบุ้งลวก
- ถั่วงอก

ราคาเพียง 65 บาท เท่านั้น!

มาลองชิมกันได้แล้ววันนี้',
        'excerpt' => 'เมนูใหม่! ก๋วยเตี๋ยวเรือพิเศษ พร้อมเครื่องเคียงมากมาย ราคาเพียง 65 บาท',
        'is_published' => true,
        'is_featured' => false,
        'sort_order' => 2,
        'published_at' => now()->subDays(1),
        'created_by' => $admin->id,
    ]);

    $news3 = News::create([
        'title' => 'ประกาศเวลาทำการใหม่',
        'content' => 'เรียน ลูกค้าทุกท่าน

ทางร้านขอประกาศเปลี่ยนแปลงเวลาทำการใหม่ ดังนี้

วันจันทร์ - วันศุกร์: 08:00 - 20:00 น.
วันเสาร์ - วันอาทิตย์: 07:00 - 21:00 น.

หยุดทำการในวันพระ

ขออภัยในความไม่สะดวก และขอบคุณสำหรับความเข้าใจ',
        'excerpt' => 'ประกาศเปลี่ยนแปลงเวลาทำการใหม่ เปิดให้บริการทุกวัน',
        'is_published' => true,
        'is_featured' => false,
        'sort_order' => 3,
        'published_at' => now()->subDays(3),
        'created_by' => $admin->id,
    ]);

    $news4 = News::create([
        'title' => 'โปรโมชั่นพิเศษ! ซื้อ 2 ชาม ลด 10 บาท',
        'content' => 'ขอแจ้งโปรโมชั่นพิเศษสำหรับลูกค้าทุกท่าน

"ซื้อก๋วยเตี๋ยวเรือ 2 ชาม ลดทันที 10 บาท"

เงื่อนไข:
- ใช้ได้ทุกเมนูก๋วยเตี๋ยวเรือ
- ไม่สามารถใช้ร่วมกับโปรโมชั่นอื่นได้
- ใช้ได้ถึงสิ้นเดือนนี้เท่านั้น

รีบมาใช้บริการก่อนหมดโปรโมชั่น!',
        'excerpt' => 'โปรโมชั่นพิเศษ! ซื้อก๋วยเตี๋ยวเรือ 2 ชาม ลดทันที 10 บาท',
        'is_published' => true,
        'is_featured' => true,
        'sort_order' => 4,
        'published_at' => now()->subHours(2),
        'created_by' => $admin->id,
    ]);

    $news5 = News::create([
        'title' => 'ขอบคุณลูกค้าทุกท่านที่ให้การสนับสนุน',
        'content' => 'ขอขอบคุณลูกค้าทุกท่านที่ให้การสนับสนุนร้านก๋วยเตี๋ยวเรือเข้าท่า

ด้วยความที่ร้านของเราได้รับการตอบรับที่ดีจากลูกค้าทุกท่าน ทำให้เรามีกำลังใจในการพัฒนาและปรับปรุงคุณภาพอาหารและบริการให้ดียิ่งขึ้น

เราจะคงไว้ซึ่งรสชาติดั้งเดิมของก๋วยเตี๋ยวเรือ พร้อมทั้งใส่ใจในทุกรายละเอียด เพื่อให้ลูกค้าทุกท่านได้รับประสบการณ์การรับประทานอาหารที่ดีที่สุด

ขอบคุณอีกครั้งสำหรับความไว้วางใจ',
        'excerpt' => 'ขอขอบคุณลูกค้าทุกท่านที่ให้การสนับสนุนร้านก๋วยเตี๋ยวเรือเข้าท่า',
        'is_published' => true,
        'is_featured' => false,
        'sort_order' => 5,
        'published_at' => now()->subDays(5),
        'created_by' => $admin->id,
    ]);

    echo "Created 5 sample news articles:\n";
    echo "1. {$news1->title}\n";
    echo "2. {$news2->title}\n";
    echo "3. {$news3->title}\n";
    echo "4. {$news4->title}\n";
    echo "5. {$news5->title}\n";
} else {
    echo "News already exists. Current news:\n";
    $allNews = News::all();
    foreach ($allNews as $news) {
        echo "- {$news->title} (Published: " . ($news->is_published ? 'Yes' : 'No') . ")\n";
    }
}

echo "\nTotal published news: " . News::published()->count() . "\n";
