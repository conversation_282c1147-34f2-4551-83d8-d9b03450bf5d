<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Drop about_pages table - not used in application (uses static data instead)
        Schema::dropIfExists('about_pages');
        
        // Drop contact_pages table - not used in application (uses static data instead)
        Schema::dropIfExists('contact_pages');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Recreate about_pages table
        Schema::create('about_pages', function (Blueprint $table) {
            $table->id();
            $table->string('title')->default('เกี่ยวกับเรา');
            $table->text('content')->nullable();
            $table->string('hero_image')->nullable();
            $table->string('story_image')->nullable();
            $table->string('team_image')->nullable();
            $table->string('gallery_image_1')->nullable();
            $table->string('gallery_image_2')->nullable();
            $table->string('gallery_image_3')->nullable();
            $table->text('our_story')->nullable();
            $table->text('our_mission')->nullable();
            $table->text('our_vision')->nullable();
            $table->text('main_content')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Recreate contact_pages table
        Schema::create('contact_pages', function (Blueprint $table) {
            $table->id();
            $table->string('title')->default('ติดต่อเรา');
            $table->text('description')->nullable();
            $table->string('hero_image')->nullable();
            $table->string('default_background')->nullable();
            $table->string('address')->nullable();
            $table->string('phone')->nullable();
            $table->string('mobile')->nullable();
            $table->string('email')->nullable();
            $table->string('line_id')->nullable();
            $table->string('facebook')->nullable();
            $table->string('instagram')->nullable();
            $table->time('open_time')->nullable();
            $table->time('close_time')->nullable();
            $table->json('open_days')->nullable();
            $table->text('special_hours')->nullable();
            $table->text('map_embed')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->text('directions')->nullable();
            $table->string('location_image')->nullable();
            $table->string('interior_image')->nullable();
            $table->string('parking_image')->nullable();
            $table->text('parking_info')->nullable();
            $table->text('public_transport')->nullable();
            $table->text('additional_info')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }
};
