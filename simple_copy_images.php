<?php

echo "=== เริ่มคัดลอกรูปภาพ ===\n";

// ตรวจสอบโฟลเดอร์
$storageDir = __DIR__ . '/storage/app/public';
$publicDir = __DIR__ . '/public/images';

echo "Storage directory: $storageDir\n";
echo "Public directory: $publicDir\n";

if (!is_dir($storageDir)) {
    echo "❌ ไม่พบโฟลเดอร์ storage: $storageDir\n";
    exit(1);
}

// สร้างโฟลเดอร์ public/images
if (!is_dir($publicDir)) {
    if (mkdir($publicDir, 0755, true)) {
        echo "✅ สร้างโฟลเดอร์ $publicDir\n";
    } else {
        echo "❌ ไม่สามารถสร้างโฟลเดอร์ $publicDir\n";
        exit(1);
    }
}

// โฟลเดอร์ที่ต้องคัดลอก
$folders = ['menu-items', 'categories', 'restaurant', 'about-page', 'contact-page', 'gallery', 'news'];

$totalCopied = 0;

foreach ($folders as $folder) {
    $sourceFolder = $storageDir . '/' . $folder;
    $destFolder = $publicDir . '/' . $folder;
    
    echo "\n📁 ตรวจสอบ: $folder\n";
    
    if (!is_dir($sourceFolder)) {
        echo "  ⚠️  ไม่พบ: $sourceFolder\n";
        continue;
    }
    
    // สร้างโฟลเดอร์ปลายทาง
    if (!is_dir($destFolder)) {
        if (mkdir($destFolder, 0755, true)) {
            echo "  ✅ สร้าง: $destFolder\n";
        }
    }
    
    // คัดลอกไฟล์
    $files = glob($sourceFolder . '/*');
    foreach ($files as $file) {
        if (is_file($file)) {
            $filename = basename($file);
            $destFile = $destFolder . '/' . $filename;
            
            if (copy($file, $destFile)) {
                echo "  ✅ คัดลอก: $filename\n";
                $totalCopied++;
            } else {
                echo "  ❌ ไม่สามารถคัดลอก: $filename\n";
            }
        }
    }
}

echo "\n=== สรุป ===\n";
echo "📋 คัดลอกแล้ว: $totalCopied ไฟล์\n";
echo "🎉 เสร็จสิ้น!\n";
