@echo off
echo === คัดลอกรูปภาพจาก Storage ไปยัง Public ===
echo.

REM สร้างโฟลเดอร์ public\images หากยังไม่มี
if not exist "public\images" (
    mkdir "public\images"
    echo ✅ สร้างโฟลเดอร์ public\images
)

REM คัดลอกโฟลเดอร์ต่างๆ
echo.
echo 📁 คัดลอกโฟลเดอร์ menu-items...
if exist "storage\app\public\menu-items" (
    xcopy "storage\app\public\menu-items" "public\images\menu-items" /E /I /Y
    echo ✅ คัดลอก menu-items เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ menu-items
)

echo.
echo 📁 คัดลอกโฟลเดอร์ categories...
if exist "storage\app\public\categories" (
    xcopy "storage\app\public\categories" "public\images\categories" /E /I /Y
    echo ✅ คัดลอก categories เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ categories
)

echo.
echo 📁 คัดลอกโฟลเดอร์ restaurant...
if exist "storage\app\public\restaurant" (
    xcopy "storage\app\public\restaurant" "public\images\restaurant" /E /I /Y
    echo ✅ คัดลอก restaurant เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ restaurant
)

echo.
echo 📁 คัดลอกโฟลเดอร์ about-page...
if exist "storage\app\public\about-page" (
    xcopy "storage\app\public\about-page" "public\images\about-page" /E /I /Y
    echo ✅ คัดลอก about-page เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ about-page
)

echo.
echo 📁 คัดลอกโฟลเดอร์ contact-page...
if exist "storage\app\public\contact-page" (
    xcopy "storage\app\public\contact-page" "public\images\contact-page" /E /I /Y
    echo ✅ คัดลอก contact-page เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ contact-page
)

echo.
echo 📁 คัดลอกโฟลเดอร์ gallery...
if exist "storage\app\public\gallery" (
    xcopy "storage\app\public\gallery" "public\images\gallery" /E /I /Y
    echo ✅ คัดลอก gallery เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ gallery
)

echo.
echo 📁 คัดลอกโฟลเดอร์ news...
if exist "storage\app\public\news" (
    xcopy "storage\app\public\news" "public\images\news" /E /I /Y
    echo ✅ คัดลอก news เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ news
)

REM สร้างไฟล์ .htaccess
echo.
echo 📝 สร้างไฟล์ .htaccess...
echo # Allow access to images > "public\images\.htaccess"
echo ^<IfModule mod_rewrite.c^> >> "public\images\.htaccess"
echo     RewriteEngine On >> "public\images\.htaccess"
echo ^</IfModule^> >> "public\images\.htaccess"
echo. >> "public\images\.htaccess"
echo # Set proper MIME types >> "public\images\.htaccess"
echo ^<IfModule mod_mime.c^> >> "public\images\.htaccess"
echo     AddType image/jpeg .jpg .jpeg >> "public\images\.htaccess"
echo     AddType image/png .png >> "public\images\.htaccess"
echo     AddType image/gif .gif >> "public\images\.htaccess"
echo     AddType image/webp .webp >> "public\images\.htaccess"
echo     AddType image/svg+xml .svg >> "public\images\.htaccess"
echo ^</IfModule^> >> "public\images\.htaccess"

echo ✅ สร้างไฟล์ .htaccess เรียบร้อย

echo.
echo === สรุป ===
echo 🎉 คัดลอกรูปภาพเรียบร้อยแล้ว!
echo 📁 รูปภาพทั้งหมดอยู่ใน public\images\
echo 🚀 ตอนนี้เว็บไซต์สามารถแสดงรูปภาพได้ในทุกเครื่อง PC
echo 📝 รูปภาพจะถูกเข้าถึงผ่าน URL: /images/folder/filename.jpg
echo.
pause
