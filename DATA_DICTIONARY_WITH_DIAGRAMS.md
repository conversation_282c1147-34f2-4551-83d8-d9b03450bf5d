# 📊 DATA DICTIONARY - ระบบร้านก๋วยเตี๋ยวเรือเข้าท่า
## LastNoodle Restaurant System

**วันที่สร้าง:** 31 กรกฎาคม 2025  
**เวอร์ชัน:** 2.0 (Clean Version with Diagrams)  
**ฐานข้อมูล:** MySQL  
**จำนวนตาราง:** 7 ตาราง (ใช้งานจริงทั้งหมด)

---

## 🗂️ สารบัญตาราง

| ลำดับ | ชื่อตาราง | คำอธิบาย | Model | Controller |
|-------|-----------|----------|-------|------------|
| 1 | `admin` | ตารางผู้ใช้งานระบบ | User | AuthController |
| 2 | `categories` | ตารางหมวดหมู่เมนู | Category | CategoryController |
| 3 | `menu_items` | ตารางรายการเมนูอาหาร | MenuItem | MenuItemController |
| 4 | `migrations` | ตารางประวัติ migration | - | - |
| 5 | `news` | ตารางข่าวสาร | News | NewsController |
| 6 | `restaurant_info` | ตารางข้อมูลร้าน | RestaurantInfo | RestaurantInfoController |
| 7 | `sessions` | ตารางเซสชันผู้ใช้ | - | - |

---

## 🏗️ โครงสร้างฐานข้อมูล (Entity Relationship Diagram)

```mermaid
erDiagram
    admin {
        BIGINT id PK "รหัสผู้ใช้งานอัตโนมัติ"
        VARCHAR name "ชื่อผู้ใช้งาน"
        VARCHAR email UK "อีเมลผู้ใช้งาน (ไม่ซ้ำ)"
        TIMESTAMP email_verified_at "วันที่ยืนยันอีเมล"
        VARCHAR password "รหัสผ่าน (เข้ารหัส)"
        VARCHAR role "บทบาท (admin, user)"
        VARCHAR remember_token "Token จำรหัสผ่าน"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    categories {
        BIGINT id PK "รหัสหมวดหมู่อัตโนมัติ"
        VARCHAR name "ชื่อหมวดหมู่"
        VARCHAR slug UK "URL Slug (ไม่ซ้ำ)"
        TEXT description "รายละเอียดหมวดหมู่"
        VARCHAR icon "ไอคอน FontAwesome"
        VARCHAR image "รูปภาพหมวดหมู่"
        INT sort_order "ลำดับการแสดง"
        BOOLEAN is_active "สถานะการใช้งาน"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    menu_items {
        BIGINT id PK "รหัสเมนูอัตโนมัติ"
        BIGINT category_id FK "รหัสหมวดหมู่"
        VARCHAR name "ชื่อเมนู"
        TEXT description "รายละเอียดเมนู"
        DECIMAL price "ราคา (บาท)"
        VARCHAR image "รูปภาพเมนู"
        BOOLEAN is_featured "เมนูแนะนำ"
        BOOLEAN is_active "สถานะการใช้งาน"
        INT sort_order "ลำดับการแสดง"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    news {
        BIGINT id PK "รหัสข่าวอัตโนมัติ"
        VARCHAR title "หัวข้อข่าว"
        TEXT content "เนื้อหาข่าว"
        TEXT excerpt "สรุปข่าว"
        VARCHAR image "รูปภาพประกอบ"
        BOOLEAN is_published "สถานะเผยแพร่"
        BOOLEAN is_featured "ข่าวแนะนำ"
        INT sort_order "ลำดับการแสดง"
        TIMESTAMP published_at "วันที่เผยแพร่"
        BIGINT created_by FK "ผู้สร้าง"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    restaurant_info {
        BIGINT id PK "รหัสข้อมูลร้าน"
        VARCHAR name "ชื่อร้าน"
        TEXT description "คำอธิบายร้าน"
        TEXT tagline "สโลแกนร้าน"
        VARCHAR address "ที่อยู่"
        VARCHAR phone "เบอร์โทรศัพท์"
        VARCHAR mobile "เบอร์มือถือ"
        VARCHAR email "อีเมล"
        VARCHAR website "เว็บไซต์"
        VARCHAR facebook "Facebook URL"
        VARCHAR line "Line ID"
        VARCHAR instagram "Instagram URL"
        TIME open_time "เวลาเปิด"
        TIME close_time "เวลาปิด"
        JSON open_days "วันที่เปิด (array)"
        VARCHAR logo "โลโก้ร้าน"
        VARCHAR cover_image "รูปปกร้าน"
        VARCHAR background_image "รูปพื้นหลัง"
        TEXT map_embed "Google Maps embed code"
        DECIMAL latitude "ละติจูด"
        DECIMAL longitude "ลองจิจูด"
        BOOLEAN is_active "สถานะการใช้งาน"
        TIMESTAMP created_at "วันที่สร้าง"
        TIMESTAMP updated_at "วันที่แก้ไขล่าสุด"
    }

    sessions {
        VARCHAR id PK "รหัสเซสชัน"
        BIGINT user_id FK "รหัสผู้ใช้งาน"
        VARCHAR ip_address "IP Address"
        TEXT user_agent "User Agent"
        LONGTEXT payload "ข้อมูลเซสชัน"
        INT last_activity "กิจกรรมล่าสุด (timestamp)"
    }

    migrations {
        INT id PK "รหัส migration อัตโนมัติ"
        VARCHAR migration "ชื่อไฟล์ migration"
        INT batch "รอบการรัน migration"
    }

    %% Relationships
    admin ||--o{ news : "creates"
    admin ||--o{ sessions : "has"
    categories ||--o{ menu_items : "contains"
```

---

## 🏛️ สถาปัตยกรรมระบบ (System Architecture)

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Public Website] --> B[Admin Panel]
        A --> C[Authentication]
    end

    subgraph "Controller Layer"
        D[HomeController] --> E[MenuController]
        E --> F[NewsController]
        F --> G[ContactController]
        H[AuthController] --> I[Admin Controllers]
        I --> J[CategoryController]
        J --> K[MenuItemController]
        K --> L[NewsController Admin]
        L --> M[RestaurantInfoController]
    end

    subgraph "Model Layer"
        N[User Model] --> O[Category Model]
        O --> P[MenuItem Model]
        P --> Q[News Model]
        Q --> R[RestaurantInfo Model]
    end

    subgraph "Database Layer"
        S[(admin)] --> T[(categories)]
        T --> U[(menu_items)]
        S --> V[(news)]
        S --> W[(sessions)]
        X[(restaurant_info)] --> Y[(migrations)]
    end

    subgraph "Static Data"
        Z[About Page Data] --> AA[Contact Page Data]
    end

    %% Connections
    A --> D
    B --> H
    C --> H
    D --> N
    E --> O
    E --> P
    F --> Q
    G --> AA
    I --> N
    J --> O
    K --> P
    L --> Q
    M --> R
    N --> S
    O --> T
    P --> U
    Q --> V
    R --> X
    D --> Z
```

---

## 🧹 การทำความสะอาดฐานข้อมูล (Database Cleanup Process)

```mermaid
flowchart TD
    A[เริ่มต้น: 16 ตาราง] --> B{วิเคราะห์การใช้งาน}
    
    B --> C[ตารางที่ใช้งาน: 7 ตาราง]
    B --> D[ตารางที่ไม่ใช้: 9 ตาราง]
    
    C --> C1[admin - ผู้ใช้งาน]
    C --> C2[categories - หมวดหมู่]
    C --> C3[menu_items - เมนูอาหาร]
    C --> C4[news - ข่าวสาร]
    C --> C5[restaurant_info - ข้อมูลร้าน]
    C --> C6[sessions - เซสชัน Laravel]
    C --> C7[migrations - ประวัติ migration]
    
    D --> D1[ลบแล้ว: 7 ตาราง]
    D --> D2[ควรลบ: 2 ตาราง]
    
    D1 --> D1A[hero_sliders]
    D1 --> D1B[images]
    D1 --> D1C[font_settings]
    D1 --> D1D[failed_jobs]
    D1 --> D1E[personal_access_tokens]
    D1 --> D1F[password_resets]
    D1 --> D1G[settings]
    
    D2 --> D2A[about_pages]
    D2 --> D2B[contact_pages]
    
    D2A --> E{มี Model?}
    D2B --> F{มี Admin Routes?}
    
    E --> E1[❌ ไม่มี Model]
    F --> F1[❌ ไม่มี Admin Routes]
    
    E1 --> G[ใช้ Static Data แทน]
    F1 --> G
    
    G --> H[คำสั่งลบ]
    
    H --> H1[php artisan db:cleanup --force]
    H --> H2[DROP TABLE about_pages]
    H --> H3[DROP TABLE contact_pages]
    
    H1 --> I[ผลลัพธ์: 7 ตารางที่ใช้งานจริง]
    H2 --> I
    H3 --> I
    
    I --> J[✅ ฐานข้อมูลสะอาด]
```

---

## 🔗 ความสัมพันธ์ตาราง (Table Relationships)

### 1. admin (1) → news (N)
- **Foreign Key:** `news.created_by` → `admin.id`
- **ความหมาย:** ผู้ใช้งานหนึ่งคนสามารถสร้างข่าวได้หลายข่าว

### 2. admin (1) → sessions (N)
- **Foreign Key:** `sessions.user_id` → `admin.id`
- **ความหมาย:** ผู้ใช้งานหนึ่งคนสามารถมีเซสชันได้หลายเซสชัน

### 3. categories (1) → menu_items (N)
- **Foreign Key:** `menu_items.category_id` → `categories.id`
- **ความหมาย:** หมวดหมู่หนึ่งหมวดสามารถมีเมนูได้หลายเมนู

---

## 📋 รายละเอียดตารางข้อมูล

### 1. ตาราง: admin (ผู้ใช้งานระบบ)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|-------------|------|----------|
| 1 | id | BIGINT(20) | PK | รหัสผู้ใช้งานอัตโนมัติ |
| 2 | name | VARCHAR(255) | | ชื่อผู้ใช้งาน |
| 3 | email | VARCHAR(255) | UQ | อีเมลผู้ใช้งาน (ไม่ซ้ำ) |
| 4 | email_verified_at | TIMESTAMP | | วันที่ยืนยันอีเมล |
| 5 | password | VARCHAR(255) | | รหัสผ่าน (เข้ารหัส) |
| 6 | role | VARCHAR(255) | | บทบาท (admin, user) |
| 7 | remember_token | VARCHAR(100) | | Token จำรหัสผ่าน |
| 8 | created_at | TIMESTAMP | | วันที่สร้าง |
| 9 | updated_at | TIMESTAMP | | วันที่แก้ไขล่าสุด |

**Technical Info:**
- **Model:** `App\Models\User` (table = 'admin')
- **Controller:** `App\Http\Controllers\AuthController`
- **Routes:** Authentication routes (/login, /logout, /auto-admin)

### 2. ตาราง: categories (หมวดหมู่เมนู)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|-------------|------|----------|
| 1 | id | BIGINT(20) | PK | รหัสหมวดหมู่อัตโนมัติ |
| 2 | name | VARCHAR(255) | | ชื่อหมวดหมู่ |
| 3 | slug | VARCHAR(255) | UQ | URL Slug (ไม่ซ้ำ) |
| 4 | description | TEXT | | รายละเอียดหมวดหมู่ |
| 5 | icon | VARCHAR(255) | | ไอคอน FontAwesome |
| 6 | image | VARCHAR(255) | | รูปภาพหมวดหมู่ |
| 7 | sort_order | INT(11) | | ลำดับการแสดง (เริ่มต้น: 0) |
| 8 | is_active | BOOLEAN | | สถานะการใช้งาน (เริ่มต้น: true) |
| 9 | created_at | TIMESTAMP | | วันที่สร้าง |
| 10 | updated_at | TIMESTAMP | | วันที่แก้ไขล่าสุด |

**Technical Info:**
- **Model:** `App\Models\Category`
- **Controller:** `App\Http\Controllers\Admin\CategoryController`
- **Routes:** `/admin/categories` (CRUD operations)

### 3. ตาราง: menu_items (รายการเมนูอาหาร)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|-------------|------|----------|
| 1 | id | BIGINT(20) | PK | รหัสเมนูอัตโนมัติ |
| 2 | category_id | BIGINT(20) | FK | รหัสหมวดหมู่ (อ้างอิง categories) |
| 3 | name | VARCHAR(255) | | ชื่อเมนู |
| 4 | description | TEXT | | รายละเอียดเมนู |
| 5 | price | DECIMAL(8,2) | | ราคา (บาท) |
| 6 | image | VARCHAR(255) | | รูปภาพเมนู |
| 7 | is_featured | BOOLEAN | | เมนูแนะนำ (เริ่มต้น: false) |
| 8 | is_active | BOOLEAN | | สถานะการใช้งาน (เริ่มต้น: true) |
| 9 | sort_order | INT(11) | | ลำดับการแสดง (เริ่มต้น: 0) |
| 10 | created_at | TIMESTAMP | | วันที่สร้าง |
| 11 | updated_at | TIMESTAMP | | วันที่แก้ไขล่าสุด |

**Foreign Key:** `category_id REFERENCES categories(id) ON DELETE CASCADE`

**Technical Info:**
- **Model:** `App\Models\MenuItem`
- **Controller:** `App\Http\Controllers\Admin\MenuItemController`
- **Routes:** `/admin/menu-items` (CRUD operations)

### 4. ตาราง: news (ข่าวสาร)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|-------------|------|----------|
| 1 | id | BIGINT(20) | PK | รหัสข่าวอัตโนมัติ |
| 2 | title | VARCHAR(255) | | หัวข้อข่าว |
| 3 | content | TEXT | | เนื้อหาข่าว |
| 4 | excerpt | TEXT | | สรุปข่าว |
| 5 | image | VARCHAR(255) | | รูปภาพประกอบ |
| 6 | is_published | BOOLEAN | | สถานะเผยแพร่ (เริ่มต้น: true) |
| 7 | is_featured | BOOLEAN | | ข่าวแนะนำ (เริ่มต้น: false) |
| 8 | sort_order | INT(11) | | ลำดับการแสดง (เริ่มต้น: 0) |
| 9 | published_at | TIMESTAMP | | วันที่เผยแพร่ |
| 10 | created_by | BIGINT(20) | FK | ผู้สร้าง (อ้างอิง admin) |
| 11 | created_at | TIMESTAMP | | วันที่สร้าง |
| 12 | updated_at | TIMESTAMP | | วันที่แก้ไขล่าสุด |

**Foreign Key:** `created_by REFERENCES admin(id)`

**Technical Info:**
- **Model:** `App\Models\News`
- **Controller:** `App\Http\Controllers\Admin\NewsController`, `App\Http\Controllers\NewsController`
- **Routes:** `/admin/news` (Admin CRUD), `/news` (Public view)

### 5. ตาราง: restaurant_info (ข้อมูลร้าน)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|-------------|------|----------|
| 1 | id | BIGINT(20) | PK | รหัสข้อมูลร้าน |
| 2 | name | VARCHAR(255) | | ชื่อร้าน (เริ่มต้น: ร้านก๋วยเตี๋ยวเรือเข้าท่า) |
| 3 | description | TEXT | | คำอธิบายร้าน |
| 4 | tagline | TEXT | | สโลแกนร้าน |
| 5 | address | VARCHAR(255) | | ที่อยู่ |
| 6 | phone | VARCHAR(255) | | เบอร์โทรศัพท์ |
| 7 | mobile | VARCHAR(255) | | เบอร์มือถือ |
| 8 | email | VARCHAR(255) | | อีเมล |
| 9 | website | VARCHAR(255) | | เว็บไซต์ |
| 10 | facebook | VARCHAR(255) | | Facebook URL |
| 11 | line | VARCHAR(255) | | Line ID |
| 12 | instagram | VARCHAR(255) | | Instagram URL |
| 13 | open_time | TIME | | เวลาเปิด |
| 14 | close_time | TIME | | เวลาปิด |
| 15 | open_days | JSON | | วันที่เปิด (array) |
| 16 | logo | VARCHAR(255) | | โลโก้ร้าน |
| 17 | cover_image | VARCHAR(255) | | รูปปกร้าน |
| 18 | background_image | VARCHAR(255) | | รูปพื้นหลัง |
| 19 | map_embed | TEXT | | Google Maps embed code |
| 20 | latitude | DECIMAL(10,8) | | ละติจูด |
| 21 | longitude | DECIMAL(11,8) | | ลองจิจูด |
| 22 | is_active | BOOLEAN | | สถานะการใช้งาน (เริ่มต้น: true) |
| 23 | created_at | TIMESTAMP | | วันที่สร้าง |
| 24 | updated_at | TIMESTAMP | | วันที่แก้ไขล่าสุด |

**Technical Info:**
- **Model:** `App\Models\RestaurantInfo`
- **Controller:** `App\Http\Controllers\Admin\RestaurantInfoController`
- **Routes:** `/admin/restaurant-info` (Admin management)

### 6. ตาราง: sessions (เซสชันผู้ใช้ - ระบบ Laravel)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|-------------|------|----------|
| 1 | id | VARCHAR(255) | PK | รหัสเซสชัน |
| 2 | user_id | BIGINT(20) | FK | รหัสผู้ใช้งาน (อ้างอิง admin) |
| 3 | ip_address | VARCHAR(45) | | IP Address |
| 4 | user_agent | TEXT | | User Agent |
| 5 | payload | LONGTEXT | | ข้อมูลเซสชัน |
| 6 | last_activity | INT(11) | | กิจกรรมล่าสุด (timestamp) |

**Foreign Key:** `user_id REFERENCES admin(id) ON DELETE CASCADE`
**หมายเหตุ:** ตารางระบบ Laravel - จำเป็นสำหรับการจัดการ session

### 7. ตาราง: migrations (ประวัติ migration - ระบบ Laravel)

| ลำดับ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์ | คำอธิบาย |
|-------|-----------|-------------|------|----------|
| 1 | id | INT(10) | PK | รหัส migration อัตโนมัติ |
| 2 | migration | VARCHAR(255) | | ชื่อไฟล์ migration |
| 3 | batch | INT(11) | | รอบการรัน migration |

**หมายเหตุ:** ตารางระบบ Laravel - จำเป็นสำหรับการทำงานของ migration system

---

## 🗑️ ตารางที่ถูกลบออกจากระบบ

### ตารางที่ลบแล้ว (9 ตาราง):
1. **hero_sliders** - ไม่ใช้สไลเดอร์
2. **images** - ใช้ storage แทน
3. **font_settings** - ใช้ CSS แทน
4. **failed_jobs** - ไม่ใช้ queue
5. **personal_access_tokens** - ไม่ใช้ API
6. **password_resets** - ไม่ใช้รีเซ็ตรหัสผ่าน
7. **settings** - ไม่ได้ใช้งาน
8. **about_pages** - ไม่มี Model, ใช้ static data แทน
9. **contact_pages** - ไม่มี Model, ใช้ static data แทน

---

## 📄 หน้าที่ใช้ Static Data

### หน้าเกี่ยวกับเรา (/about)
- **Controller:** `HomeController::about()`
- **ข้อมูล:** Static data ใน Controller
- **เหตุผล:** ไม่ต้องการระบบจัดการเนื้อหา

### หน้าติดต่อเรา (/contact)
- **Controller:** `ContactController::index()`
- **ข้อมูล:** Static data ใน Controller + RestaurantInfo
- **เหตุผล:** ข้อมูลติดต่อมาจาก RestaurantInfo

---

## 🛠️ เครื่องมือจัดการฐานข้อมูล

### คำสั่ง Database Cleanup:
```bash
php artisan db:cleanup --dry-run    # ตรวจสอบตารางที่ไม่ใช้
php artisan db:cleanup --force      # ลบตารางที่ไม่ใช้
```

### การ Backup และ Restore:
```bash
mysqldump -u root -p lastnoodletest > backup.sql
mysql -u root -p lastnoodletest < backup.sql
```

### การตรวจสอบตาราง:
```sql
SHOW TABLES;                        -- แสดงตารางทั้งหมด
SELECT COUNT(*) FROM table_name;    -- นับจำนวนข้อมูลในตาราง
```

---

## 📊 สรุป

✅ **ฐานข้อมูลสะอาด** - มีเฉพาะ 7 ตารางที่ใช้งานจริง
✅ **โครงสร้างชัดเจน** - มี Diagram แสดงความสัมพันธ์
✅ **เอกสารครบถ้วน** - รายละเอียดทุกตารางพร้อม Technical Info
✅ **ง่ายต่อการบำรุงรักษา** - มีเครื่องมือและคำสั่งที่จำเป็น

**ระบบพร้อมใช้งานและมีประสิทธิภาพสูงสุด! 🚀**
