===============================================================================
                    DATA DICTIONARY - ระบบร้านก๋วยเตี๋ยวเรือเข้าท่า
                              LastNoodle Restaurant System
===============================================================================

วันที่สร้าง: 24 กรกฎาคม 2025
เวอร์ชัน: 1.1
ฐานข้อมูล: MySQL
จำนวนตาราง: 7 ตาราง (ใช้งานจริง)

===============================================================================
                                สารบัญตาราง
===============================================================================

1. admin              - ตารางผู้ใช้งานระบบ (เปลี่ยนชื่อจาก users)
2. categories         - ตารางหมวดหมู่เมนู
3. menu_items         - ตารางรายการเมนูอาหาร
4. migrations         - ตารางประวัติ migration (ระบบ Laravel)
5. news               - ตารางข่าวสาร
6. restaurant_info    - ตารางข้อมูลร้าน
7. sessions           - ตารางเซสชันผู้ใช้ (ระบบ Laravel)

===============================================================================
                            รายละเอียดตารางข้อมูล
===============================================================================

1. ตาราง: admin (ผู้ใช้งานระบบ)
===============================================================================
ลำดับ | ชื่อฟิลด์           | ประเภทข้อมูล      | คีย์  | คำอธิบาย
------|-------------------|------------------|------|---------------------------
1     | id                | BIGINT(20)       | PK   | รหัสผู้ใช้งานอัตโนมัติ
2     | name              | VARCHAR(255)     |      | ชื่อผู้ใช้งาน
3     | email             | VARCHAR(255)     | UQ   | อีเมลผู้ใช้งาน (ไม่ซ้ำ)
4     | email_verified_at | TIMESTAMP        |      | วันที่ยืนยันอีเมล
5     | password          | VARCHAR(255)     |      | รหัสผ่าน (เข้ารหัส)
6     | role              | VARCHAR(255)     |      | บทบาท (admin, user)
7     | remember_token    | VARCHAR(100)     |      | Token จำรหัสผ่าน
8     | created_at        | TIMESTAMP        |      | วันที่สร้าง
9     | updated_at        | TIMESTAMP        |      | วันที่แก้ไขล่าสุด

2. ตาราง: categories (หมวดหมู่เมนู)
===============================================================================
ลำดับ | ชื่อฟิลด์           | ประเภทข้อมูล      | คีย์  | คำอธิบาย
------|-------------------|------------------|------|---------------------------
1     | id                | BIGINT(20)       | PK   | รหัสหมวดหมู่อัตโนมัติ
2     | name              | VARCHAR(255)     |      | ชื่อหมวดหมู่
3     | slug              | VARCHAR(255)     | UQ   | URL Slug (ไม่ซ้ำ)
4     | description       | TEXT             |      | รายละเอียดหมวดหมู่
5     | icon              | VARCHAR(255)     |      | ไอคอน FontAwesome
6     | image               | VARCHAR(255)     |      | รูปภาพหมวดหมู่
7     | sort_order        | INT(11)          |      | ลำดับการแสดง (เริ่มต้น: 0)
8     | is_active         | BOOLEAN          |      | สถานะการใช้งาน (เริ่มต้น: true)
9     | created_at        | TIMESTAMP        |      | วันที่สร้าง
10    | updated_at        | TIMESTAMP        |      | วันที่แก้ไขล่าสุด

3. ตาราง: menu_items (รายการเมนูอาหาร)
===============================================================================
ลำดับ | ชื่อฟิลด์           | ประเภทข้อมูล      | คีย์  | คำอธิบาย
------|-------------------|------------------|------|---------------------------
1     | id                | BIGINT(20)       | PK   | รหัสเมนูอัตโนมัติ
2     | category_id       | BIGINT(20)       | FK   | รหัสหมวดหมู่ (อ้างอิง categories)
3     | name              | VARCHAR(255)     |      | ชื่อเมนู
4     | description       | TEXT             |      | รายละเอียดเมนู
5     | price             | DECIMAL(8,2)     |      | ราคา (บาท)
6     | image             | VARCHAR(255)     |      | รูปภาพเมนู
7     | is_featured       | BOOLEAN          |      | เมนูแนะนำ (เริ่มต้น: false)
8     | is_active         | BOOLEAN          |      | สถานะการใช้งาน (เริ่มต้น: true)
9     | sort_order        | INT(11)          |      | ลำดับการแสดง (เริ่มต้น: 0)
10    | created_at        | TIMESTAMP        |      | วันที่สร้าง
11    | updated_at        | TIMESTAMP        |      | วันที่แก้ไขล่าสุด

Foreign Key: category_id REFERENCES categories(id) ON DELETE CASCADE

4. ตาราง: migrations (ประวัติ migration - ระบบ Laravel)
===============================================================================
ลำดับ | ชื่อฟิลด์           | ประเภทข้อมูล      | คีย์  | คำอธิบาย
------|-------------------|------------------|------|---------------------------
1     | id                | INT(10)          | PK   | รหัส migration อัตโนมัติ
2     | migration         | VARCHAR(255)     |      | ชื่อไฟล์ migration
3     | batch             | INT(11)          |      | รอบการรัน migration

5. ตาราง: news (ข่าวสาร)
===============================================================================
ลำดับ | ชื่อฟิลด์           | ประเภทข้อมูล      | คีย์  | คำอธิบาย
------|-------------------|------------------|------|---------------------------
1     | id                | BIGINT(20)       | PK   | รหัสข่าวอัตโนมัติ
2     | title             | VARCHAR(255)     |      | หัวข้อข่าว
3     | content           | TEXT             |      | เนื้อหาข่าว
4     | excerpt           | TEXT             |      | สรุปข่าว
5     | image             | VARCHAR(255)     |      | รูปภาพประกอบ
6     | is_published      | BOOLEAN          |      | สถานะเผยแพร่ (เริ่มต้น: true)
7     | is_featured       | BOOLEAN          |      | ข่าวแนะนำ (เริ่มต้น: false)
8     | sort_order        | INT(11)          |      | ลำดับการแสดง (เริ่มต้น: 0)
9     | published_at      | TIMESTAMP        |      | วันที่เผยแพร่
10    | created_by        | BIGINT(20)       | FK   | ผู้สร้าง (อ้างอิง admin)
11    | created_at        | TIMESTAMP        |      | วันที่สร้าง
12    | updated_at        | TIMESTAMP        |      | วันที่แก้ไขล่าสุด

Foreign Key: created_by REFERENCES admin(id)

6. ตาราง: restaurant_info (ข้อมูลร้าน)
===============================================================================
ลำดับ | ชื่อฟิลด์           | ประเภทข้อมูล      | คีย์  | คำอธิบาย
------|-------------------|------------------|------|---------------------------
1     | id                | BIGINT(20)       | PK   | รหัสข้อมูลร้าน
2     | name              | VARCHAR(255)     |      | ชื่อร้าน (เริ่มต้น: ร้านก๋วยเตี๋ยวเรือเข้าท่า)
3     | description       | TEXT             |      | คำอธิบายร้าน
4     | tagline           | TEXT             |      | สโลแกนร้าน
5     | address           | VARCHAR(255)     |      | ที่อยู่
6     | phone             | VARCHAR(255)     |      | เบอร์โทรศัพท์
7     | mobile            | VARCHAR(255)     |      | เบอร์มือถือ
8     | email             | VARCHAR(255)     |      | อีเมล
9     | website           | VARCHAR(255)     |      | เว็บไซต์
10    | facebook          | VARCHAR(255)     |      | Facebook URL
11    | line              | VARCHAR(255)     |      | Line ID
12    | instagram         | VARCHAR(255)     |      | Instagram URL
13    | open_time         | TIME             |      | เวลาเปิด
14    | close_time        | TIME             |      | เวลาปิด
15    | open_days         | JSON             |      | วันที่เปิด (array)
16    | logo              | VARCHAR(255)     |      | โลโก้ร้าน
17    | cover_image       | VARCHAR(255)     |      | รูปปกร้าน
18    | background_image  | VARCHAR(255)     |      | รูปพื้นหลัง
19    | map_embed         | TEXT             |      | Google Maps embed code
20    | latitude          | DECIMAL(10,8)    |      | ละติจูด
21    | longitude         | DECIMAL(11,8)    |      | ลองจิจูด
22    | is_active         | BOOLEAN          |      | สถานะการใช้งาน (เริ่มต้น: true)
23    | created_at        | TIMESTAMP        |      | วันที่สร้าง
24    | updated_at        | TIMESTAMP        |      | วันที่แก้ไขล่าสุด

7. ตาราง: sessions (เซสชันผู้ใช้ - ระบบ Laravel)
===============================================================================
ลำดับ | ชื่อฟิลด์           | ประเภทข้อมูล      | คีย์  | คำอธิบาย
------|-------------------|------------------|------|---------------------------
1     | id                | VARCHAR(255)     | PK   | รหัสเซสชัน
2     | user_id           | BIGINT(20)       | FK   | รหัสผู้ใช้งาน (อ้างอิง admin)
3     | ip_address        | VARCHAR(45)      |      | IP Address
4     | user_agent        | TEXT             |      | User Agent
5     | payload           | LONGTEXT         |      | ข้อมูลเซสชัน
6     | last_activity     | INT(11)          |      | กิจกรรมล่าสุด (timestamp)

Foreign Key: user_id REFERENCES admin(id) ON DELETE CASCADE

===============================================================================
                                ความสัมพันธ์ตาราง
===============================================================================

1. admin (1) -> news (N)
   - admin.id = news.created_by
   - ผู้ใช้งานหนึ่งคนสามารถสร้างข่าวได้หลายข่าว

2. admin (1) -> sessions (N)
   - admin.id = sessions.user_id
   - ผู้ใช้งานหนึ่งคนสามารถมีเซสชันได้หลายเซสชัน

3. categories (1) -> menu_items (N)
   - categories.id = menu_items.category_id
   - หมวดหมู่หนึ่งหมวดสามารถมีเมนูได้หลายเมนู

===============================================================================
                                    หมายเหตุ
===============================================================================

1. ตารางที่ถูกลบออกจากระบบ:
   - hero_sliders (ไม่ใช้สไลเดอร์)
   - images (ใช้ storage แทน)
   - font_settings (ใช้ CSS แทน)
   - failed_jobs (ไม่ใช้ queue)
   - personal_access_tokens (ไม่ใช้ API)
   - password_resets (ไม่ใช้รีเซ็ตรหัสผ่าน)
   - settings (ไม่ได้ใช้งาน)

2. ตารางที่ควรลบออก (มีตารางแต่ไม่ได้ใช้งานจริง):
   - about_pages (ไม่มี Model, ไม่มี admin routes, ใช้ static data)
   - contact_pages (ไม่มี Model, ไม่มี admin routes, ใช้ static data)

3. การเปลี่ยนแปลงสำคัญ:
   - ตาราง users ถูกเปลี่ยนชื่อเป็น admin
   - เพิ่มฟิลด์ icon ในตาราง categories
   - เพิ่มฟิลด์ tagline และ background_image ในตาราง restaurant_info

4. ข้อมูลเริ่มต้น:
   - มีผู้ใช้งาน admin: <EMAIL>
   - มีข้อมูลร้าน: ร้านก๋วยเตี๋ยวเรือเข้าท่า
   - มีหมวดหมู่เมนู: เมนูแนะนำ, ของทานเล่น, เครื่องดื่ม
   - มีข่าวสารตัวอย่าง

===============================================================================
                                  สิ้นสุดเอกสาร
===============================================================================
