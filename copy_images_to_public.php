<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

echo "=== คัดลอกรูปภาพจาก Storage ไปยัง Public Directory ===\n";

// กำหนดโฟลเดอร์ที่ต้องคัดลอก
$folders = [
    'menu-items',
    'categories', 
    'restaurant',
    'about-page',
    'contact-page',
    'gallery',
    'news'
];

$storageBasePath = storage_path('app/public');
$publicBasePath = public_path('images');

// สร้างโฟลเดอร์ public/images หากยังไม่มี
if (!File::exists($publicBasePath)) {
    File::makeDirectory($publicBasePath, 0755, true);
    echo "✅ สร้างโฟลเดอร์ {$publicBasePath}\n";
}

$totalCopied = 0;
$totalSkipped = 0;

foreach ($folders as $folder) {
    $sourcePath = $storageBasePath . DIRECTORY_SEPARATOR . $folder;
    $destinationPath = $publicBasePath . DIRECTORY_SEPARATOR . $folder;
    
    echo "\n📁 ตรวจสอบโฟลเดอร์: {$folder}\n";
    
    if (!File::exists($sourcePath)) {
        echo "  ⚠️  ไม่พบโฟลเดอร์ต้นทาง: {$sourcePath}\n";
        continue;
    }
    
    // สร้างโฟลเดอร์ปลายทางหากยังไม่มี
    if (!File::exists($destinationPath)) {
        File::makeDirectory($destinationPath, 0755, true);
        echo "  ✅ สร้างโฟลเดอร์ปลายทาง: {$destinationPath}\n";
    }
    
    // หาไฟล์ทั้งหมดในโฟลเดอร์
    $files = File::allFiles($sourcePath);
    
    if (empty($files)) {
        echo "  📭 ไม่มีไฟล์ในโฟลเดอร์นี้\n";
        continue;
    }
    
    foreach ($files as $file) {
        $relativePath = str_replace($sourcePath . DIRECTORY_SEPARATOR, '', $file->getPathname());
        $destinationFile = $destinationPath . DIRECTORY_SEPARATOR . $relativePath;
        
        // สร้างโฟลเดอร์ย่อยหากจำเป็น
        $destinationDir = dirname($destinationFile);
        if (!File::exists($destinationDir)) {
            File::makeDirectory($destinationDir, 0755, true);
        }
        
        // ตรวจสอบว่าไฟล์ปลายทางมีอยู่แล้วหรือไม่
        if (File::exists($destinationFile)) {
            // เปรียบเทียบขนาดไฟล์
            if (File::size($file->getPathname()) === File::size($destinationFile)) {
                echo "  ⏭️  ข้าม: {$relativePath} (มีอยู่แล้วและขนาดเท่ากัน)\n";
                $totalSkipped++;
                continue;
            }
        }
        
        // คัดลอกไฟล์
        if (File::copy($file->getPathname(), $destinationFile)) {
            echo "  ✅ คัดลอก: {$relativePath}\n";
            $totalCopied++;
        } else {
            echo "  ❌ ไม่สามารถคัดลอก: {$relativePath}\n";
        }
    }
}

echo "\n=== สรุปผลการคัดลอก ===\n";
echo "📋 ไฟล์ที่คัดลอกแล้ว: {$totalCopied} ไฟล์\n";
echo "⏭️  ไฟล์ที่ข้าม: {$totalSkipped} ไฟล์\n";

// สร้างไฟล์ .htaccess สำหรับ Apache (ถ้าจำเป็น)
$htaccessPath = $publicBasePath . DIRECTORY_SEPARATOR . '.htaccess';
if (!File::exists($htaccessPath)) {
    $htaccessContent = "# Allow access to images\n<IfModule mod_rewrite.c>\n    RewriteEngine On\n</IfModule>\n\n# Set proper MIME types\n<IfModule mod_mime.c>\n    AddType image/jpeg .jpg .jpeg\n    AddType image/png .png\n    AddType image/gif .gif\n    AddType image/webp .webp\n    AddType image/svg+xml .svg\n</IfModule>\n\n# Enable compression\n<IfModule mod_deflate.c>\n    AddOutputFilterByType DEFLATE image/svg+xml\n</IfModule>";
    
    File::put($htaccessPath, $htaccessContent);
    echo "✅ สร้างไฟล์ .htaccess สำหรับการจัดการรูปภาพ\n";
}

echo "\n🎉 เสร็จสิ้นการคัดลอกรูปภาพ!\n";
echo "📝 หมายเหตุ: รูปภาพทั้งหมดถูกคัดลอกไปยัง public/images/ แล้ว\n";
echo "🚀 ตอนนี้เว็บไซต์สามารถแสดงรูปภาพได้ในทุกเครื่อง PC โดยไม่ต้องพึ่งพา storage link\n";
