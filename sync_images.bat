@echo off
echo === ซิงค์รูปภาพใหม่จาก Storage ไปยัง Public ===
echo.

REM ตรวจสอบว่ามีโฟลเดอร์ public\images หรือไม่
if not exist "public\images" (
    echo ❌ ไม่พบโฟลเดอร์ public\images
    echo 💡 กรุณารันสคริปต์ copy_images.bat ก่อน
    pause
    exit /b 1
)

echo 🔄 กำลังซิงค์รูปภาพใหม่...
echo.

REM ซิงค์โฟลเดอร์ต่างๆ (เฉพาะไฟล์ใหม่)
echo 📁 ซิงค์ menu-items...
if exist "storage\app\public\menu-items" (
    xcopy "storage\app\public\menu-items" "public\images\menu-items" /E /I /Y /D
    echo ✅ ซิงค์ menu-items เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ menu-items
)

echo.
echo 📁 ซิงค์ categories...
if exist "storage\app\public\categories" (
    xcopy "storage\app\public\categories" "public\images\categories" /E /I /Y /D
    echo ✅ ซิงค์ categories เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ categories
)

echo.
echo 📁 ซิงค์ restaurant...
if exist "storage\app\public\restaurant" (
    xcopy "storage\app\public\restaurant" "public\images\restaurant" /E /I /Y /D
    echo ✅ ซิงค์ restaurant เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ restaurant
)

echo.
echo 📁 ซิงค์ about-page...
if exist "storage\app\public\about-page" (
    xcopy "storage\app\public\about-page" "public\images\about-page" /E /I /Y /D
    echo ✅ ซิงค์ about-page เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ about-page
)

echo.
echo 📁 ซิงค์ contact-page...
if exist "storage\app\public\contact-page" (
    xcopy "storage\app\public\contact-page" "public\images\contact-page" /E /I /Y /D
    echo ✅ ซิงค์ contact-page เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ contact-page
)

echo.
echo 📁 ซิงค์ gallery...
if exist "storage\app\public\gallery" (
    xcopy "storage\app\public\gallery" "public\images\gallery" /E /I /Y /D
    echo ✅ ซิงค์ gallery เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ gallery
)

echo.
echo 📁 ซิงค์ news...
if exist "storage\app\public\news" (
    xcopy "storage\app\public\news" "public\images\news" /E /I /Y /D
    echo ✅ ซิงค์ news เรียบร้อย
) else (
    echo ⚠️  ไม่พบโฟลเดอร์ news
)

echo.
echo === สรุป ===
echo 🎉 ซิงค์รูปภาพเรียบร้อยแล้ว!
echo 📝 หมายเหตุ: ใช้ /D flag เพื่อคัดลอกเฉพาะไฟล์ที่ใหม่กว่า
echo 💡 สามารถรันสคริปต์นี้ได้ทุกครั้งหลังจากอัพโหลดรูปภาพใหม่
echo.
pause
