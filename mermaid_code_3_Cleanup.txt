flowchart TD
    %% Start
    START[🚀 เริ่มต้น: 16 ตาราง] --> ANALYZE{🔍 วิเคราะห์การใช้งาน}
    
    %% Analysis Results
    ANALYZE --> USED[✅ ตารางที่ใช้งาน<br/>7 ตาราง]
    ANALYZE --> UNUSED[❌ ตารางที่ไม่ใช้<br/>9 ตาราง]
    
    %% Used Tables (Vertical alignment)
    USED --> U1[admin<br/>ผู้ใช้งาน]
    USED --> U2[categories<br/>หมวดหมู่]
    USED --> U3[menu_items<br/>เมนูอาหาร]
    USED --> U4[news<br/>ข่าวสาร]
    USED --> U5[restaurant_info<br/>ข้อมูลร้าน]
    USED --> U6[sessions<br/>เซสชัน Laravel]
    USED --> U7[migrations<br/>ประวัติ migration]
    
    %% Unused Tables Categories
    UNUSED --> DELETED[🗑️ ลบแล้ว<br/>7 ตาราง]
    UNUSED --> SHOULD_DELETE[⚠️ ควรลบ<br/>2 ตาราง]
    
    %% Already Deleted Tables
    DELETED --> D1[hero_sliders]
    DELETED --> D2[images]
    DELETED --> D3[font_settings]
    DELETED --> D4[failed_jobs]
    DELETED --> D5[personal_access_tokens]
    DELETED --> D6[password_resets]
    DELETED --> D7[settings]
    
    %% Should Delete Tables
    SHOULD_DELETE --> SD1[about_pages]
    SHOULD_DELETE --> SD2[contact_pages]
    
    %% Analysis Questions
    SD1 --> Q1{มี Model?}
    SD2 --> Q2{มี Admin Routes?}
    
    Q1 --> A1[❌ ไม่มี Model]
    Q2 --> A2[❌ ไม่มี Admin Routes]
    
    A1 --> REASON[💡 ใช้ Static Data แทน]
    A2 --> REASON
    
    %% Action
    REASON --> ACTION[🛠️ คำสั่งลบ]
    
    ACTION --> CMD1[php artisan db:cleanup --force]
    ACTION --> CMD2[DROP TABLE about_pages]
    ACTION --> CMD3[DROP TABLE contact_pages]
    
    %% Result
    CMD1 --> RESULT[🎯 ผลลัพธ์<br/>7 ตารางที่ใช้งานจริง]
    CMD2 --> RESULT
    CMD3 --> RESULT
    
    RESULT --> SUCCESS[🎉 ฐานข้อมูลสะอาด]
    
    %% Styling
    classDef startNode fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    classDef usedNode fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    classDef unusedNode fill:#ffcdd2,stroke:#c62828,stroke-width:2px
    classDef deletedNode fill:#f8bbd9,stroke:#ad1457,stroke-width:2px
    classDef actionNode fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef resultNode fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    classDef questionNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px

    class START startNode
    class USED,U1,U2,U3,U4,U5,U6,U7 usedNode
    class UNUSED,SHOULD_DELETE,SD1,SD2 unusedNode
    class DELETED,D1,D2,D3,D4,D5,D6,D7 deletedNode
    class ACTION,CMD1,CMD2,CMD3 actionNode
    class RESULT,SUCCESS resultNode
    class ANALYZE,Q1,Q2 questionNode
