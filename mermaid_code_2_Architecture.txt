graph TD
    %% Frontend Layer
    subgraph FL["🌐 Frontend Layer"]
        direction LR
        PW[Public Website]
        AP[Admin Panel]
        AU[Authentication]
    end

    %% Controller Layer
    subgraph CL["🎮 Controller Layer"]
        direction TB
        subgraph PC["Public Controllers"]
            HC[HomeController]
            MC[MenuController]
            NC[NewsController]
            CC[ContactController]
        end
        subgraph AC["Admin Controllers"]
            AuthC[AuthController]
            CatC[CategoryController]
            MIC[MenuItemController]
            ANC[Admin NewsController]
            RIC[RestaurantInfoController]
        end
    end

    %% Model Layer
    subgraph ML["📊 Model Layer"]
        direction LR
        UM[User Model]
        CM[Category Model]
        MIM[MenuItem Model]
        NM[News Model]
        RIM[RestaurantInfo Model]
    end

    %% Database Layer
    subgraph DL["🗄️ Database Layer"]
        direction TB
        subgraph UT["User Tables"]
            ADM[(admin)]
            SES[(sessions)]
        end
        subgraph MT["Menu Tables"]
            CAT[(categories)]
            MIT[(menu_items)]
        end
        subgraph CT["Content Tables"]
            NEW[(news)]
            RES[(restaurant_info)]
            MIG[(migrations)]
        end
    end

    %% Static Data
    subgraph SD["📄 Static Data"]
        direction LR
        APD[About Page Data]
        CPD[Contact Page Data]
    end

    %% Vertical Connections (Clean Lines)
    PW -.-> HC
    AP -.-> AuthC
    AU -.-> AuthC
    
    HC -.-> UM
    HC -.-> RIM
    HC -.-> APD
    
    MC -.-> CM
    MC -.-> MIM
    
    NC -.-> NM
    
    CC -.-> RIM
    CC -.-> CPD
    
    CatC -.-> CM
    MIC -.-> MIM
    ANC -.-> NM
    RIC -.-> RIM
    
    UM -.-> ADM
    CM -.-> CAT
    MIM -.-> MIT
    NM -.-> NEW
    RIM -.-> RES

    %% Relationships
    ADM -.-> NEW
    ADM -.-> SES
    CAT -.-> MIT

    %% Styling
    classDef frontend fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef controller fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef model fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef database fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef static fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class PW,AP,AU frontend
    class HC,MC,NC,CC,AuthC,CatC,MIC,ANC,RIC controller
    class UM,CM,MIM,NM,RIM model
    class ADM,SES,CAT,MIT,NEW,RES,MIG database
    class APD,CPD static
