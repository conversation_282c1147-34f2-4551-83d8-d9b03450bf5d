# Data Dictionary - ระบบร้านก๋วยเตี๋ยวเรือเข้าท่า

## ตารางที่ 3.1 แสดงการรายละเอียดตารางข้อมูลข่าวสาร (News)

| ลำดับที่ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์หลัก | คำอธิบาย |
|---------|----------|-------------|---------|----------|
| 1 | id | Int (11) | PK | รหัสข่าวสารอัตโนมัติ |
| 2 | title | Varchar (255) | | หัวข้อข่าวสาร |
| 3 | content | Text | | เนื้อหาข่าวสาร |
| 4 | image | Varchar (255) | | รูปภาพประกอบ |
| 5 | is_featured | Boolean | | ข่าวสารแนะนำ |
| 6 | is_active | Boolean | | สถานะการใช้งาน |
| 7 | created_at | Timestamp | | วันที่สร้าง |
| 8 | updated_at | Timestamp | | วันที่แก้ไข |

## ตารางที่ 3.2 แสดงการรายละเอียดตารางหมวดหมู่เมนู (Categories)

| ลำดับที่ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์หลัก | คำอธิบาย |
|---------|----------|-------------|---------|----------|
| 1 | id | Int (11) | PK | รหัสประเภทหมวดหมู่ |
| 2 | name | Varchar (255) | | ชื่อประเภทหมวดหมู่ |
| 3 | slug | Varchar (255) | | URL Slug |
| 4 | description | Text | | รายละเอียดหมวดหมู่ |
| 5 | image | Varchar (255) | | รูปภาพหมวดหมู่ |
| 6 | icon | Varchar (100) | | ไอคอนหมวดหมู่ |
| 7 | sort_order | Int (11) | | ลำดับการแสดง |
| 8 | is_active | Boolean | | สถานะการใช้งาน |
| 9 | created_at | Timestamp | | วันที่สร้าง |
| 10 | updated_at | Timestamp | | วันที่แก้ไข |

## ตารางที่ 3.3 แสดงการรายละเอียดตารางรายการอาหาร (Menu Items)

| ลำดับที่ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์หลัก | คำอธิบาย |
|---------|----------|-------------|---------|----------|
| 1 | id | Int (11) | PK | รหัสรายการอาหาร |
| 2 | category_id | Int (11) | FK | รหัสประเภทหมวดหมู่ |
| 3 | name | Varchar (255) | | ชื่อรายการอาหาร |
| 4 | description | Text | | รายละเอียดอาหาร |
| 5 | price | Decimal (8,2) | | ราคาอาหาร |
| 6 | image | Varchar (255) | | รูปภาพอาหาร |
| 7 | is_featured | Boolean | | เมนูแนะนำ |
| 8 | is_active | Boolean | | สถานะการใช้งาน |
| 9 | sort_order | Int (11) | | ลำดับการแสดง |
| 10 | created_at | Timestamp | | วันที่สร้าง |
| 11 | updated_at | Timestamp | | วันที่แก้ไข |

## ตารางที่ 3.4 แสดงการรายละเอียดตารางข้อมูลร้าน (Restaurant Info)

| ลำดับที่ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์หลัก | คำอธิบาย |
|---------|----------|-------------|---------|----------|
| 1 | id | Int (11) | PK | รหัสข้อมูลร้าน |
| 2 | name | Varchar (255) | | ชื่อร้าน |
| 3 | description | Text | | รายละเอียดร้าน |
| 4 | address | Text | | ที่อยู่ร้าน |
| 5 | phone | Varchar (20) | | เบอร์โทรศัพท์ |
| 6 | email | Varchar (100) | | อีเมล |
| 7 | opening_hours | Text | | เวลาเปิด-ปิด |
| 8 | facebook_url | Varchar (255) | | Facebook URL |
| 9 | line_id | Varchar (100) | | Line ID |
| 10 | google_maps_url | Text | | Google Maps URL |
| 11 | tagline | Varchar (255) | | คำขวัญร้าน |
| 12 | background_image | Varchar (255) | | รูปพื้นหลัง |
| 13 | created_at | Timestamp | | วันที่สร้าง |
| 14 | updated_at | Timestamp | | วันที่แก้ไข |

## ตารางที่ 3.5 แสดงการรายละเอียดตารางหน้าเกี่ยวกับเรา (About Pages)

| ลำดับที่ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์หลัก | คำอธิบาย |
|---------|----------|-------------|---------|----------|
| 1 | id | Int (11) | PK | รหัสหน้าเกี่ยวกับเรา |
| 2 | title | Varchar (255) | | หัวข้อหน้า |
| 3 | subtitle | Varchar (255) | | หัวข้อรอง |
| 4 | main_content | Text | | เนื้อหาหลัก |
| 5 | our_story | Text | | เรื่องราวของเรา |
| 6 | hero_image | Varchar (255) | | รูปภาพหลัก |
| 7 | story_image | Varchar (255) | | รูปภาพเรื่องราว |
| 8 | gallery_image_1 | Varchar (255) | | รูปภาพแกลเลอรี่ 1 |
| 9 | gallery_image_2 | Varchar (255) | | รูปภาพแกลเลอรี่ 2 |
| 10 | gallery_image_3 | Varchar (255) | | รูปภาพแกลเลอรี่ 3 |
| 11 | created_at | Timestamp | | วันที่สร้าง |
| 12 | updated_at | Timestamp | | วันที่แก้ไข |

## ตารางที่ 3.6 แสดงการรายละเอียดตารางหน้าติดต่อเรา (Contact Pages)

| ลำดับที่ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์หลัก | คำอธิบาย |
|---------|----------|-------------|---------|----------|
| 1 | id | Int (11) | PK | รหัสหน้าติดต่อเรา |
| 2 | title | Varchar (255) | | หัวข้อหน้า |
| 3 | subtitle | Varchar (255) | | หัวข้อรอง |
| 4 | content | Text | | เนื้อหา |
| 5 | background_image | Varchar (255) | | รูปพื้นหลัง |
| 6 | created_at | Timestamp | | วันที่สร้าง |
| 7 | updated_at | Timestamp | | วันที่แก้ไข |

## ตารางที่ 3.7 แสดงการรายละเอียดตารางผู้ใช้งาน (Admin)

| ลำดับที่ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์หลัก | คำอธิบาย |
|---------|----------|-------------|---------|----------|
| 1 | id | Int (11) | PK | รหัสผู้ใช้งาน |
| 2 | name | Varchar (255) | | ชื่อผู้ใช้งาน |
| 3 | email | Varchar (255) | | อีเมล |
| 4 | password | Varchar (255) | | รหัสผ่าน |
| 5 | email_verified_at | Timestamp | | วันที่ยืนยันอีเมล |
| 6 | remember_token | Varchar (100) | | Token จำรหัสผ่าน |
| 7 | created_at | Timestamp | | วันที่สร้าง |
| 8 | updated_at | Timestamp | | วันที่แก้ไข |

## ตารางที่ 3.8 แสดงการรายละเอียดตารางการตั้งค่า (Settings)

| ลำดับที่ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์หลัก | คำอธิบาย |
|---------|----------|-------------|---------|----------|
| 1 | id | Int (11) | PK | รหัสการตั้งค่า |
| 2 | key | Varchar (255) | | คีย์การตั้งค่า |
| 3 | value | Text | | ค่าการตั้งค่า |
| 4 | description | Text | | คำอธิบาย |
| 5 | created_at | Timestamp | | วันที่สร้าง |
| 6 | updated_at | Timestamp | | วันที่แก้ไข |

## ตารางที่ 3.9 แสดงการรายละเอียดตารางเซสชัน (Sessions)

| ลำดับที่ | ชื่อฟิลด์ | ประเภทข้อมูล | คีย์หลัก | คำอธิบาย |
|---------|----------|-------------|---------|----------|
| 1 | id | Varchar (255) | PK | รหัสเซสชัน |
| 2 | user_id | Int (11) | FK | รหัสผู้ใช้งาน |
| 3 | ip_address | Varchar (45) | | IP Address |
| 4 | user_agent | Text | | User Agent |
| 5 | payload | Text | | ข้อมูลเซสชัน |
| 6 | last_activity | Int (11) | | กิจกรรมล่าสุด |

---

## ตารางที่ถูกลบออก (Unused Tables)

### Tables ที่ไม่จำเป็นและถูกลบออก:
- **hero_sliders** - ไม่ใช้สไลด์หน้าแรกแล้ว
- **images** - ไม่ใช้ระบบจัดการรูปภาพแยก
- **font_settings** - ไม่ใช้การตั้งค่าฟอนต์แยก
- **failed_jobs** - ไม่ใช้ queue system
- **personal_access_tokens** - ไม่ใช้ API tokens
- **password_resets** - ไม่ใช้ระบบรีเซ็ตรหัสผ่าน

### สาเหตุการลบ:
1. **ลดความซับซ้อน** - เหลือแค่ฟีเจอร์หลักที่จำเป็น
2. **ประสิทธิภาพ** - ลด database overhead
3. **ความปลอดภัย** - ลดจุดเข้าถึงที่ไม่จำเป็น
4. **การบำรุงรักษา** - ง่ายต่อการดูแลระบบ
