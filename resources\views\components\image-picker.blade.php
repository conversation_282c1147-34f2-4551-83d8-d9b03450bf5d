@props([
    'name' => 'image',
    'label' => 'เลือกรูปภาพ',
    'value' => null,
    'category' => 'general',
    'required' => false,
    'multiple' => false
])

<div class="mb-3">
    <label for="{{ $name }}" class="form-label">
        {{ $label }}
        @if($required)
            <span class="text-danger">*</span>
        @endif
    </label>
    
    <div class="image-picker-container">
        <!-- Current Image Preview -->
        @if($value)
            <div class="current-image-preview mb-3">
                <div class="position-relative d-inline-block">
                    <img src="{{ Storage::disk('public')->url($value) }}" 
                         alt="Current image" 
                         class="img-thumbnail"
                         style="max-width: 200px; max-height: 150px;">
                    <button type="button" 
                            class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1"
                            onclick="removeCurrentImage('{{ $name }}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <input type="hidden" name="{{ $name }}" value="{{ $value }}" id="{{ $name }}_hidden">
            </div>
        @endif

        <!-- Image Picker Buttons -->
        <div class="btn-group" role="group">
            <button type="button"
                    class="btn btn-outline-secondary"
                    onclick="openFileUpload('{{ $name }}')">
                <i class="fas fa-upload me-2"></i>อัปโหลดใหม่
            </button>
        </div>

        <!-- Hidden File Input -->
        <input type="file" 
               id="{{ $name }}_file" 
               class="d-none" 
               accept="image/*"
               {{ $multiple ? 'multiple' : '' }}
               onchange="handleFileUpload('{{ $name }}', this)">

        <!-- Hidden Input for Selected Image -->
        @if(!$value)
            <input type="hidden" name="{{ $name }}" id="{{ $name }}_hidden">
        @endif
    </div>
</div>





@push('scripts')
<script>
function openFileUpload(fieldName) {
    document.getElementById(fieldName + '_file').click();
}

function handleFileUpload(fieldName, input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Create FormData for upload
        const formData = new FormData();
        formData.append('image', file);
        formData.append('category', 'general'); // Default category
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        // Show loading state
        const container = document.querySelector(`[id="${fieldName}_file"]`).closest('.image-picker-container');
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'text-center py-2';
        loadingDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">กำลังอัปโหลด...</span></div> กำลังอัปโหลด...';
        container.appendChild(loadingDiv);

        // Upload file
        fetch('{{ route('admin.images.upload') }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loadingDiv.remove();
            if (data.success) {
                updateImagePreview(fieldName, data.data.url, data.data.path);
            } else {
                alert('เกิดข้อผิดพลาดในการอัปโหลด: ' + (data.message || 'ไม่ทราบสาเหตุ'));
            }
        })
        .catch(error => {
            loadingDiv.remove();
            console.error('Upload error:', error);
            alert('เกิดข้อผิดพลาดในการอัปโหลด');
        });

        // Clear input
        input.value = '';
    }
}

function removeCurrentImage(fieldName) {
    const container = document.querySelector(`input[name="${fieldName}"]`).closest('.image-picker-container');
    const preview = container.querySelector('.current-image-preview');
    if (preview) {
        preview.remove();
    }
    document.querySelector(`input[name="${fieldName}"]`).value = '';
}



function updateImagePreview(fieldName, imageUrl, imagePath) {
    const container = document.querySelector(`input[name="${fieldName}"]`).closest('.image-picker-container');

    // Remove existing preview
    const existingPreview = container.querySelector('.current-image-preview');
    if (existingPreview) {
        existingPreview.remove();
    }

    // Create new preview
    const preview = document.createElement('div');
    preview.className = 'current-image-preview mb-3';
    preview.innerHTML = `
        <div class="position-relative d-inline-block">
            <img src="${imageUrl}"
                 alt="Selected image"
                 class="img-thumbnail"
                 style="max-width: 200px; max-height: 150px;">
            <button type="button"
                    class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1"
                    onclick="removeCurrentImage('${fieldName}')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <input type="hidden" name="${fieldName}" value="${imagePath}" id="${fieldName}_hidden">
    `;

    // Insert preview before buttons
    const buttons = container.querySelector('.btn-group');
    container.insertBefore(preview, buttons);
}
</script>
@endpush
