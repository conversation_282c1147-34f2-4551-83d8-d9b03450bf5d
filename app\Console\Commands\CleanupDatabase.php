<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CleanupDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:cleanup {--dry-run : Show what would be deleted without actually deleting} {--force : Force deletion without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up unused database tables';

    /**
     * Tables that are actively used by the application
     */
    protected $usedTables = [
        'admin',
        'categories',
        'menu_items',
        'restaurant_info',
        'sessions',
        'migrations',
        'news'
    ];

    /**
     * Tables that are safe to remove
     */
    protected $unusedTables = [
        'hero_sliders',
        'images',
        'font_settings',
        'failed_jobs',
        'personal_access_tokens',
        'password_resets',
        'cache',
        'jobs',
        'job_batches',
        'settings',  // Not used in the application
        'about_pages',  // Has table but no Model, no admin routes, uses static data
        'contact_pages'  // Has table but no Model, no admin routes, uses static data
    ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔍 Analyzing database tables...');
        $this->newLine();

        // Get all tables in database
        $allTables = $this->getAllTables();
        
        if (empty($allTables)) {
            $this->error('Could not retrieve table list from database');
            return 1;
        }

        $this->info('📊 Current tables in database:');
        foreach ($allTables as $table) {
            $status = $this->getTableStatus($table);
            $this->line("  {$status} {$table}");
        }
        $this->newLine();

        // Find unused tables
        $tablesToRemove = array_intersect($allTables, $this->unusedTables);
        
        if (empty($tablesToRemove)) {
            $this->info('✅ No unused tables found. Database is clean!');
            return 0;
        }

        $this->warn('🗑️  Found ' . count($tablesToRemove) . ' unused table(s):');
        foreach ($tablesToRemove as $table) {
            $rowCount = $this->getTableRowCount($table);
            $this->line("  • {$table} ({$rowCount} rows)");
        }
        $this->newLine();

        if ($this->option('dry-run')) {
            $this->info('🔍 DRY RUN: No tables were actually deleted.');
            $this->info('Run without --dry-run to perform the cleanup.');
            return 0;
        }

        // Confirm deletion
        if (!$this->option('force')) {
            if (!$this->confirm('Are you sure you want to delete these tables? This action cannot be undone.')) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        // Delete tables
        $this->info('🗑️  Deleting unused tables...');
        $deleted = 0;
        
        foreach ($tablesToRemove as $table) {
            try {
                Schema::dropIfExists($table);
                $this->info("  ✅ Deleted: {$table}");
                $deleted++;
            } catch (\Exception $e) {
                $this->error("  ❌ Failed to delete {$table}: " . $e->getMessage());
            }
        }

        $this->newLine();
        $this->info("🎉 Cleanup completed! Deleted {$deleted} table(s).");
        
        // Show remaining tables
        $this->info('📊 Remaining tables:');
        $remainingTables = $this->getAllTables();
        foreach ($remainingTables as $table) {
            $status = $this->getTableStatus($table);
            $this->line("  {$status} {$table}");
        }

        return 0;
    }

    /**
     * Get all tables in the database
     */
    protected function getAllTables(): array
    {
        try {
            $tables = DB::select('SHOW TABLES');
            $tableNames = [];
            
            foreach ($tables as $table) {
                $tableArray = (array) $table;
                $tableNames[] = array_values($tableArray)[0];
            }
            
            return $tableNames;
        } catch (\Exception $e) {
            $this->error('Error getting table list: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get table status (used/unused/system)
     */
    protected function getTableStatus(string $table): string
    {
        if (in_array($table, $this->usedTables)) {
            return '✅';
        } elseif (in_array($table, $this->unusedTables)) {
            return '🗑️ ';
        } elseif ($table === 'migrations') {
            return '🔧';
        } else {
            return '❓';
        }
    }

    /**
     * Get row count for a table
     */
    protected function getTableRowCount(string $table): int
    {
        try {
            $result = DB::select("SELECT COUNT(*) as count FROM `{$table}`");
            return $result[0]->count ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }
}
