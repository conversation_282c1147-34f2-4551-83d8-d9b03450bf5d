<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use App\Helpers\ImageHelper;

class CheckImageStorage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:check {--fix : Attempt to fix issues automatically}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check image storage configuration and fix common issues';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔍 Checking image storage configuration...');
        $this->newLine();

        $issues = [];
        $fixes = [];

        // Check 1: Storage link
        $this->checkStorageLink($issues, $fixes);

        // Check 2: Storage directories
        $this->checkStorageDirectories($issues, $fixes);

        // Check 3: Permissions
        $this->checkPermissions($issues, $fixes);

        // Check 4: Sample images
        $this->checkSampleImages($issues, $fixes);

        // Display results
        $this->displayResults($issues, $fixes);

        // Apply fixes if requested
        if ($this->option('fix') && !empty($fixes)) {
            $this->applyFixes($fixes);
        }

        return empty($issues) ? 0 : 1;
    }

    private function checkStorageLink(&$issues, &$fixes)
    {
        $this->info('📁 Checking storage link...');

        $storageLinkPath = public_path('storage');
        $storageAppPublicPath = storage_path('app/public');

        if (!File::exists($storageLinkPath)) {
            $issues[] = 'Storage link does not exist';
            $fixes[] = [
                'type' => 'storage_link',
                'description' => 'Create storage link',
                'command' => 'php artisan storage:link'
            ];
            $this->error('  ❌ Storage link missing');
        } elseif (!ImageHelper::isStorageLinkWorking()) {
            $issues[] = 'Storage link is broken';
            $fixes[] = [
                'type' => 'storage_link_fix',
                'description' => 'Recreate storage link',
                'command' => 'php artisan storage:link --force'
            ];
            $this->error('  ❌ Storage link is broken');
        } else {
            $this->info('  ✅ Storage link is working');
        }
    }

    private function checkStorageDirectories(&$issues, &$fixes)
    {
        $this->info('📂 Checking storage directories...');

        $directories = [
            'menu-items',
            'categories', 
            'restaurant',
            'about-page',
            'contact-page',
            'gallery'
        ];

        foreach ($directories as $dir) {
            if (!Storage::disk('public')->exists($dir)) {
                $issues[] = "Storage directory '{$dir}' does not exist";
                $fixes[] = [
                    'type' => 'create_directory',
                    'description' => "Create directory: {$dir}",
                    'path' => $dir
                ];
                $this->warn("  ⚠️  Directory missing: {$dir}");
            } else {
                $this->info("  ✅ Directory exists: {$dir}");
            }
        }
    }

    private function checkPermissions(&$issues, &$fixes)
    {
        $this->info('🔐 Checking permissions...');

        $storageAppPublic = storage_path('app/public');
        $publicStorage = public_path('storage');

        // Check if directories are writable
        if (!is_writable($storageAppPublic)) {
            $issues[] = 'Storage directory is not writable';
            $fixes[] = [
                'type' => 'permissions',
                'description' => 'Fix storage permissions',
                'path' => $storageAppPublic
            ];
            $this->error('  ❌ Storage directory not writable');
        } else {
            $this->info('  ✅ Storage directory is writable');
        }
    }

    private function checkSampleImages(&$issues, &$fixes)
    {
        $this->info('🖼️  Checking sample images...');

        $sampleImages = [
            'images/menu/placeholder.svg',
            'images/restaurant/background.jpg',
            'images/logo/logo.jpg'
        ];

        foreach ($sampleImages as $image) {
            if (!File::exists(public_path($image))) {
                $issues[] = "Sample image missing: {$image}";
                $this->warn("  ⚠️  Missing: {$image}");
            } else {
                $this->info("  ✅ Found: {$image}");
            }
        }
    }

    private function displayResults($issues, $fixes)
    {
        $this->newLine();
        
        if (empty($issues)) {
            $this->info('🎉 All checks passed! Image storage is configured correctly.');
        } else {
            $this->error('❌ Found ' . count($issues) . ' issue(s):');
            foreach ($issues as $issue) {
                $this->line("  • {$issue}");
            }

            if (!empty($fixes)) {
                $this->newLine();
                $this->info('🔧 Available fixes:');
                foreach ($fixes as $fix) {
                    $this->line("  • {$fix['description']}");
                    if (isset($fix['command'])) {
                        $this->line("    Command: {$fix['command']}");
                    }
                }
                $this->newLine();
                $this->info('Run with --fix to apply fixes automatically');
            }
        }
    }

    private function applyFixes($fixes)
    {
        $this->info('🔧 Applying fixes...');
        $this->newLine();

        foreach ($fixes as $fix) {
            switch ($fix['type']) {
                case 'storage_link':
                case 'storage_link_fix':
                    $this->info("Creating storage link...");
                    $this->call('storage:link', ['--force' => true]);
                    break;

                case 'create_directory':
                    $this->info("Creating directory: {$fix['path']}");
                    Storage::disk('public')->makeDirectory($fix['path']);
                    break;

                case 'permissions':
                    $this->info("Fixing permissions for: {$fix['path']}");
                    // On Windows, this might not be necessary
                    if (PHP_OS_FAMILY !== 'Windows') {
                        chmod($fix['path'], 0755);
                    }
                    break;
            }
        }

        $this->newLine();
        $this->info('✅ Fixes applied successfully!');
    }
}
