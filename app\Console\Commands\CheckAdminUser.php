<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CheckAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:check {--create : Create admin user if not exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check if admin user exists and optionally create one';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔍 Checking admin user...');
        $this->newLine();

        $adminEmail = '<EMAIL>';
        $adminUser = User::where('email', $adminEmail)->first();

        if ($adminUser) {
            $this->info('✅ Admin user found:');
            $this->line("   📧 Email: {$adminUser->email}");
            $this->line("   👤 Name: {$adminUser->name}");
            $this->line("   🔑 Role: {$adminUser->role}");
            $this->line("   📅 Created: {$adminUser->created_at->format('Y-m-d H:i:s')}");
            
            if ($adminUser->role !== 'admin') {
                $this->warn('⚠️  Warning: User role is not "admin"');
                
                if ($this->confirm('Do you want to update the role to "admin"?')) {
                    $adminUser->update(['role' => 'admin']);
                    $this->info('✅ Role updated to "admin"');
                }
            }
            
            $this->newLine();
            $this->info('🚀 Auto-login URL: http://localhost:8000/auto-admin');
            
        } else {
            $this->error('❌ Admin user not found!');
            
            if ($this->option('create') || $this->confirm('Do you want to create an admin user?')) {
                $this->createAdminUser($adminEmail);
            } else {
                $this->warn('💡 Run with --create flag to create admin user automatically');
                $this->line('   php artisan admin:check --create');
            }
        }

        return 0;
    }

    /**
     * Create admin user
     */
    private function createAdminUser($email)
    {
        $this->info('🔨 Creating admin user...');

        $userData = [
            'name' => 'Administrator',
            'email' => $email,
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ];

        try {
            $user = User::create($userData);
            
            $this->info('✅ Admin user created successfully!');
            $this->line("   📧 Email: {$user->email}");
            $this->line("   👤 Name: {$user->name}");
            $this->line("   🔑 Password: admin123");
            $this->line("   🔑 Role: {$user->role}");
            $this->newLine();
            $this->info('🚀 Auto-login URL: http://localhost:8000/auto-admin');
            
        } catch (\Exception $e) {
            $this->error('❌ Failed to create admin user: ' . $e->getMessage());
            return 1;
        }
    }
}
