<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\MenuItem;
use App\Models\RestaurantInfo;


class HomeController extends Controller
{
    public function index()
    {
        $featuredMenus = MenuItem::with('category')
            ->active()
            ->featured()
            ->ordered()
            ->take(6)
            ->get();

        $categories = Category::active()->ordered()->get();
        $restaurantInfo = RestaurantInfo::getInfo();

        return view('home', compact('featuredMenus', 'categories', 'restaurantInfo'));
    }

    public function menu()
    {
        $categories = Category::active()->ordered()->get();
        $featuredMenus = MenuItem::active()->featured()->with('category')->ordered()->take(6)->get();
        $menuItems = MenuItem::active()->with('category')->ordered()->paginate(12);

        return view('menu.index', compact('categories', 'featuredMenus', 'menuItems'));
    }

    public function menuByCategory($categorySlug)
    {
        $categories = Category::active()->ordered()->get();
        $currentCategory = Category::where('slug', $categorySlug)->active()->firstOrFail();

        $featuredMenus = MenuItem::where('category_id', $currentCategory->id)
            ->active()
            ->featured()
            ->with('category')
            ->ordered()
            ->take(6)
            ->get();

        $menuItems = MenuItem::where('category_id', $currentCategory->id)
            ->active()
            ->with('category')
            ->ordered()
            ->paginate(12);

        return view('menu.index', compact('categories', 'currentCategory', 'featuredMenus', 'menuItems'));
    }

    public function about()
    {
        // Static about page data (since we removed AboutPage model)
        $aboutPage = (object) [
            'title' => 'เกี่ยวกับเรา',
            'description' => 'ร้านอาหารที่มีประวัติยาวนาน',
            'hero_image' => null,
            'default_background' => null,
            'main_content' => 'ยินดีต้อนรับสู่ร้านอาหารของเรา ที่นำเสนอรสชาติดั้งเดิมของก๋วยเตี๋ยวเรือแท้ๆ',
            'our_story' => 'เริ่มต้นจากความฝันของคุณยายที่ต้องการนำเสนอรสชาติก๋วยเตี๋ยวเรือแท้ๆ ให้คนรุ่นใหม่ได้สัมผัส ด้วยสูตรลับที่ถ่ายทอดมาจากรุ่นสู่รุ่น',
            'our_mission' => 'มุ่งมั่นให้บริการอาหารคุณภาพดี ด้วยวัตถุดิบสดใหม่และรสชาติที่ถูกปาก',
            'our_vision' => 'เป็นร้านอาหารที่ลูกค้าไว้วางใจ และเป็นที่รู้จักในด้านรสชาติดั้งเดิม',
            'story_image' => null,
            'team_image' => null,
            'team_description' => 'ทีมงานมืออาชีพที่มีประสบการณ์ในการทำอาหารมายาวนาน',
            'gallery_image_1' => null,
            'gallery_image_2' => null,
            'gallery_image_3' => null,
            'chef_image' => null,
            'chef_name' => null,
            'chef_description' => null,
            'restaurant_images' => null,
            'awards' => null,
            'certifications' => null
        ];

        $restaurantInfo = RestaurantInfo::getInfo();
        return view('about', compact('aboutPage', 'restaurantInfo'));
    }
}
