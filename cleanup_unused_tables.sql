-- ===================================================================
-- การลบตารางที่ไม่ได้ใช้งานในฐานข้อมูล lastnoodletest
-- วันที่: 31 กรกฎาคม 2025
-- ===================================================================

-- ตรวจสอบตารางที่มีอยู่ในฐานข้อมูล
SELECT 'ตารางที่มีอยู่ในฐานข้อมูล:' as status;
SHOW TABLES;

-- ตรวจสอบจำนวนข้อมูลในตารางที่จะลบ
SELECT 'จำนวนข้อมูลในตาราง about_pages:' as status;
SELECT COUNT(*) as row_count FROM about_pages;

SELECT 'จำนวนข้อมูลในตาราง contact_pages:' as status;
SELECT COUNT(*) as row_count FROM contact_pages;

-- ลบตาราง about_pages (ไม่ได้ใช้งานจริง - ใช้ static data แทน)
SELECT 'กำลังลบตาราง about_pages...' as status;
DROP TABLE IF EXISTS about_pages;

-- ลบตาราง contact_pages (ไม่ได้ใช้งานจริง - ใช้ static data แทน)
SELECT 'กำลังลบตาราง contact_pages...' as status;
DROP TABLE IF EXISTS contact_pages;

-- ตรวจสอบตารางที่เหลืออยู่หลังจากลบ
SELECT 'ตารางที่เหลืออยู่หลังจากลบ:' as status;
SHOW TABLES;

-- แสดงสรุปผลการดำเนินงาน
SELECT 'การลบตารางเสร็จสิ้น!' as status;
SELECT 'ตารางที่ลบ: about_pages, contact_pages' as summary;
SELECT 'เหตุผล: ไม่มี Model, ไม่มี admin routes, ใช้ static data แทน' as reason;

-- ===================================================================
-- หมายเหตุ:
-- 1. ตารางเหล่านี้ไม่ได้ใช้งานจริงในระบบ
-- 2. HomeController และ ContactController ใช้ static data แทน
-- 3. ไม่มีระบบจัดการข้อมูลในหน้า admin
-- 4. การลบจะไม่ส่งผลกระทบต่อการทำงานของเว็บไซต์
-- ===================================================================
