<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Admin Panel') - ร้านก๋วยเตี๋ยวเรือเข้าท่า</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2691E;
            --gold-color: #FFD700;
            --cream-color: #FFF8DC;
            --dark-brown: #654321;
        }
        
        body {
            font-family: 'Kanit', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--dark-brown) 100%);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        
        .main-content {
            margin-left: 250px;
            min-height: 100vh;
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 8px;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--dark-brown), var(--primary-color));
            transform: translateY(-1px);
        }
        
        .border-left-primary {
            border-left: 4px solid var(--primary-color) !important;
        }
        
        .border-left-success {
            border-left: 4px solid #28a745 !important;
        }
        
        .border-left-info {
            border-left: 4px solid #17a2b8 !important;
        }
        
        .border-left-warning {
            border-left: 4px solid #ffc107 !important;
        }
        
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
            }
            
            .sidebar {
                position: fixed;
                top: 0;
                left: -250px;
                width: 250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            
            .sidebar.show {
                left: 0;
            }
        }

        /* Form Switch Styles */
        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-input:focus {
            border-color: var(--secondary-color);
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(139, 69, 19, 0.25);
        }

        .form-switch .form-check-input {
            width: 2em;
            margin-left: -2.5em;
            background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='rgba%280,0,0,.25%29'/></svg>");
            background-position: left center;
            background-size: contain;
            border-radius: 2em;
            transition: background-position .15s ease-in-out;
        }

        .form-switch .form-check-input:checked {
            background-position: right center;
            background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='rgba%28255,255,255,1.0%29'/></svg>");
        }

        .form-switch .form-check-input:disabled {
            pointer-events: none;
            filter: none;
            opacity: .5;
        }
    </style>
    
    @stack('styles')

    <style>
    /* Custom SVG Icon Classes */
    .icon-boat-noodle {
        display: inline-block;
        width: 1em;
        height: 1em;
        background-image: url('{{ asset('images/icons/boat-noodle-simple.svg') }}');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        vertical-align: middle;
    }

    .icon-boat-noodle-lg {
        width: 2em;
        height: 2em;
    }

    .icon-boat-noodle-xl {
        width: 3em;
        height: 3em;
    }

    .icon-boat-noodle-2x {
        width: 2em;
        height: 2em;
    }

    .icon-boat-noodle-3x {
        width: 3em;
        height: 3em;
    }

    .icon-boat-noodle-4x {
        width: 4em;
        height: 4em;
    }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar position-fixed" style="width: 250px; z-index: 1000;">
        <div class="p-3">
            <div class="text-center mb-4">
                @php
                    $restaurantInfo = \App\Models\RestaurantInfo::getInfo();
                @endphp
                <div class="bg-white rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                    @if($restaurantInfo->logo)
                        <img src="{{ asset('storage/' . $restaurantInfo->logo) }}" alt="{{ $restaurantInfo->name ?? 'ร้านก๋วยเตี๋ยวเรือเข้าท่า' }}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 50%;">
                    @else
                        <span class="icon-boat-noodle icon-boat-noodle-2x" style="filter: hue-rotate(200deg);"></span>
                    @endif
                </div>
                <h6 class="text-white mb-0">Admin Panel</h6>
                <small class="text-white-50">{{ $restaurantInfo->name ?? 'ร้านก๋วยเตี๋ยวเรือเข้าท่า' }}</small>
            </div>
            
            <nav class="nav flex-column">

                <a class="nav-link {{ request()->routeIs('admin.categories.*') ? 'active' : '' }}" href="{{ route('admin.categories.index') }}">
                    <i class="fas fa-list"></i>
                    หมวดหมู่อาหาร
                </a>
                
                <a class="nav-link {{ request()->routeIs('admin.menu-items.*') ? 'active' : '' }}" href="{{ route('admin.menu-items.index') }}">
                    <i class="fas fa-utensils"></i>
                    เมนูอาหาร
                </a>
                
                <a class="nav-link {{ request()->routeIs('admin.news.*') ? 'active' : '' }}" href="{{ route('admin.news.index') }}">
                    <i class="fas fa-newspaper"></i>
                    ข่าวสาร
                </a>

                {{-- เมนูที่ไม่ใช้แล้ว: dashboard, hero-content, restaurant-info, about-page, contact-page - ลบออกแล้ว --}}

                {{-- เมนูสไลด์หน้าแรกและผู้ใช้งานถูกลบออกแล้ว --}}
                
                <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
                
                <a class="nav-link" href="{{ route('home') }}" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    ดูหน้าเว็บไซต์
                </a>
                
                <a class="nav-link" href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                    <i class="fas fa-sign-out-alt"></i>
                    ออกจากระบบ
                </a>
                
                <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                    @csrf
                </form>
            </nav>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
            <div class="container-fluid">
                <button class="btn btn-link d-md-none" type="button" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <div class="bg-primary rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <span>{{ Auth::user()->name }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ route('home') }}" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>ดูหน้าเว็บไซต์
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Page Content -->
        <main class="p-4">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif
            
            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif
            
            @yield('content')
        </main>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function toggleSidebar() {
            document.querySelector('.sidebar').classList.toggle('show');
        }
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.sidebar');
            const toggleBtn = document.querySelector('[onclick="toggleSidebar()"]');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !toggleBtn.contains(event.target) && 
                sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });

        // Enhanced form switch functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure all form switches work properly
            document.querySelectorAll('.form-switch .form-check-input').forEach(function(switchElement) {
                // Add click event listener
                switchElement.addEventListener('click', function(e) {
                    // Allow the default behavior (checkbox toggle)
                    // Add visual feedback
                    this.style.transition = 'all 0.15s ease-in-out';
                });

                // Add change event listener for debugging
                switchElement.addEventListener('change', function(e) {
                    console.log('Switch changed:', this.name, 'checked:', this.checked);
                });
            });
        });
    </script>

    @stack('scripts')
</body>
</html>
