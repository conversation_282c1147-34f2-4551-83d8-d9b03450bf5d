@extends('layouts.app')

@section('title', 'เมนูอาหาร - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<!-- Page Header -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-5 fw-bold mb-3">
                    <i class="fas fa-utensils me-3"></i>เมนูอาหาร
                </h1>
                <p class="lead">
                    ก๋วยเตี๋ยวเรือแท้ รสชาติดั้งเดิม และอาหารอร่อยอีกมากมาย
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Menu Categories -->
<section class="py-5">
    <div class="container">
        @if($categories->count() > 0)
            @foreach($categories as $category)
                <div class="mb-5">
                    <!-- Category Header -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex align-items-center">
                                @if($category->image)
                                    @php
                                        $imageUrl = \App\Helpers\ImageHelper::getCategoryImageUrl($category->image);
                                    @endphp
                                    <img src="{{ $imageUrl }}"
                                         alt="{{ $category->name }}"
                                         class="rounded-circle me-3"
                                         style="width: 60px; height: 60px; object-fit: cover;">
                                @else
                                    <div class="bg-primary rounded-circle me-3 d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-utensils text-white"></i>
                                    </div>
                                @endif
                                
                                <div>
                                    <h2 class="text-primary mb-1">{{ $category->name }}</h2>
                                    <p class="text-muted mb-0">{{ $category->description }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Menu Items -->
                    @if($category->menuItems->count() > 0)
                        <div class="row">
                            @foreach($category->menuItems as $item)
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card h-100 border-0 shadow-sm">
                                        @if($item->image)
                                            @php
                                                $imageUrl = \App\Helpers\ImageHelper::getMenuImageUrl($item->image);
                                            @endphp
                                            <img src="{{ $imageUrl }}"
                                                 class="card-img-top"
                                                 alt="{{ $item->name }}"
                                                 style="height: 200px; object-fit: cover; cursor: pointer; transition: transform 0.3s ease;"
                                                 data-bs-toggle="modal"
                                                 data-bs-target="#imageModal"
                                                 data-image-src="{{ $imageUrl }}"
                                                 data-image-alt="{{ $item->name }}">
                                        @else
                                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                                 style="height: 200px;">
                                                <i class="fas fa-image fa-3x text-muted"></i>
                                            </div>
                                        @endif
                                        
                                        <div class="card-body d-flex flex-column">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h5 class="card-title text-primary mb-0">{{ $item->name }}</h5>
                                                @if($item->is_featured)
                                                    <span class="badge bg-warning text-dark">
                                                        แนะนำ
                                                    </span>
                                                @endif
                                            </div>
                                            
                                            @if($item->description)
                                                <p class="card-text text-muted flex-grow-1">{{ $item->description }}</p>
                                            @endif
                                            
                                            <div class="mt-auto">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <strong class="text-primary h5 mb-0">{{ $item->formatted_price }}</strong>
                                                    @if(!$item->is_active)
                                                        <span class="badge bg-danger">ไม่พร้อมจำหน่าย</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-utensils fa-2x text-muted mb-2"></i>
                            <p class="text-muted">ยังไม่มีเมนูในหมวดหมู่นี้</p>
                        </div>
                    @endif
                </div>
                
                @if(!$loop->last)
                    <hr class="my-5">
                @endif
            @endforeach
        @else
            <div class="text-center py-5">
                <i class="fas fa-utensils fa-4x text-muted mb-4"></i>
                <h3 class="text-muted">ยังไม่มีเมนูอาหาร</h3>
                <p class="text-muted">กรุณาติดต่อเจ้าของร้านเพื่อสอบถามเมนูอาหาร</p>
            </div>
        @endif
    </div>
</section>

<!-- Call to Action -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h3 class="mb-3">พร้อมลิ้มลองแล้วหรือยัง?</h3>
                <p class="lead mb-4">
                    มาเยือนร้านเราและสัมผัสรสชาติก๋วยเตี๋ยวเรือแท้ๆ ที่คุณจะไม่มีวันลืม
                </p>
                <a href="{{ route('about') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-map-marker-alt me-2"></i>ดูที่อยู่ร้าน
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content bg-transparent border-0">
            <div class="modal-header border-0 pb-0">
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0">
                <img id="modalImage" src="" alt="" class="img-fluid rounded shadow-lg" style="max-height: 80vh;">
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
#imageModal .modal-content {
    background: transparent !important;
}

#imageModal .btn-close-white {
    background: rgba(255,255,255,0.8);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    opacity: 1;
}

#imageModal .btn-close-white:hover {
    background: rgba(255,255,255,1);
}

.card-img-top:hover {
    transform: scale(1.05);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle image modal
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');

    if (imageModal && modalImage) {
        imageModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const imageSrc = button.getAttribute('data-image-src');
            const imageAlt = button.getAttribute('data-image-alt');

            modalImage.src = imageSrc;
            modalImage.alt = imageAlt;
        });

        // Close modal when clicking on the image
        modalImage.addEventListener('click', function() {
            const modalInstance = bootstrap.Modal.getInstance(imageModal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modalInstance = bootstrap.Modal.getInstance(imageModal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        });
    }
});
</script>
@endpush
