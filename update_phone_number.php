<?php

require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\RestaurantInfo;

echo "=== อัพเดทเบอร์โทรศัพท์ ===\n";

// ตรวจสอบข้อมูลปัจจุบัน
$info = RestaurantInfo::first();

if ($info) {
    echo "ข้อมูลปัจจุบัน:\n";
    echo "- ชื่อร้าน: " . ($info->name ?? 'ไม่มี') . "\n";
    echo "- เบอร์โทรศัพท์เดิม: " . ($info->phone ?? 'ไม่มี') . "\n";
    echo "- เบอร์มือถือ: " . ($info->mobile ?? 'ไม่มี') . "\n";
    
    // อัพเดทเบอร์โทรศัพท์
    $info->phone = '056721338';
    $info->save();
    
    echo "\n=== อัพเดทเรียบร้อยแล้ว ===\n";
    echo "- เบอร์โทรศัพท์ใหม่: " . $info->phone . "\n";
    
} else {
    echo "ไม่พบข้อมูลร้าน - สร้างข้อมูลใหม่\n";
    
    // สร้างข้อมูลใหม่
    $info = RestaurantInfo::create([
        'name' => 'ร้านก๋วยเตี๋ยวเรือเข้าท่า',
        'description' => 'ก๋วยเตี๋ยวเรือต้นตำรับ รสชาติดั้งเดิม ด้วยสูตรลับเฉพาะตัว',
        'tagline' => 'ลิ้มลองรสชาติความอร่อยระดับตำนาน ที่ยังคงไว้ซึ่งเอกลักษณ์ไทย',
        'phone' => '056721338',
        'is_active' => true,
    ]);
    
    echo "สร้างข้อมูลใหม่เรียบร้อยแล้ว\n";
    echo "- เบอร์โทรศัพท์: " . $info->phone . "\n";
}

echo "\n=== เสร็จสิ้น ===\n";
