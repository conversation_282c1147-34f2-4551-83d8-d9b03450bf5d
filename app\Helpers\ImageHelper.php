<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class ImageHelper
{
    /**
     * Get the full URL for an image with fallback options
     *
     * @param string|null $imagePath
     * @param string $fallbackImage
     * @return string
     */
    public static function getImageUrl($imagePath = null, $fallbackImage = 'images/menu/placeholder.svg')
    {
        // If no image path provided, return fallback
        if (empty($imagePath)) {
            return asset($fallbackImage);
        }

        // Priority 1: Check if the image exists in public/images directory (portable solution)
        $publicImagesPath = public_path('images/' . $imagePath);
        if (File::exists($publicImagesPath)) {
            return asset('images/' . $imagePath);
        }

        // Priority 2: Check if the image exists in storage (original Laravel way)
        if (Storage::disk('public')->exists($imagePath)) {
            return asset('storage/' . $imagePath);
        }

        // Priority 3: Check if the image exists in public directory (legacy images)
        $publicPath = public_path($imagePath);
        if (File::exists($publicPath)) {
            return asset($imagePath);
        }

        // Return fallback image
        return asset($fallbackImage);
    }

    /**
     * Get menu item image URL with specific fallback
     * 
     * @param string|null $imagePath
     * @return string
     */
    public static function getMenuImageUrl($imagePath = null)
    {
        return self::getImageUrl($imagePath, 'images/menu/placeholder.svg');
    }

    /**
     * Get restaurant image URL with specific fallback
     *
     * @param string|null $imagePath
     * @return string
     */
    public static function getRestaurantImageUrl($imagePath = null)
    {
        return self::getImageUrl($imagePath, 'restaurant/เจ้าของ.jpg');
    }

    /**
     * Get category image URL with specific fallback
     * 
     * @param string|null $imagePath
     * @return string
     */
    public static function getCategoryImageUrl($imagePath = null)
    {
        return self::getImageUrl($imagePath, 'images/menu/placeholder.svg');
    }

    /**
     * Check if storage link exists and is working
     *
     * @return bool
     */
    public static function isStorageLinkWorking()
    {
        $storageLinkPath = public_path('storage');
        $storageAppPublicPath = storage_path('app/public');

        // Check if storage link exists
        if (!File::exists($storageLinkPath)) {
            return false;
        }

        // Test by checking if we can access a known directory through the link
        $testDirectories = ['menu-items', 'categories', 'restaurant'];

        foreach ($testDirectories as $dir) {
            $linkPath = $storageLinkPath . DIRECTORY_SEPARATOR . $dir;
            $realPath = $storageAppPublicPath . DIRECTORY_SEPARATOR . $dir;

            // If the directory exists in storage but not accessible through link, link is broken
            if (File::exists($realPath) && !File::exists($linkPath)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get all available image formats
     * 
     * @return array
     */
    public static function getSupportedImageFormats()
    {
        return ['jpeg', 'jpg', 'png', 'gif', 'webp', 'svg'];
    }

    /**
     * Validate image file
     * 
     * @param \Illuminate\Http\UploadedFile $file
     * @return bool
     */
    public static function validateImageFile($file)
    {
        if (!$file || !$file->isValid()) {
            return false;
        }

        $allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
        $allowedExtensions = self::getSupportedImageFormats();

        return in_array($file->getMimeType(), $allowedMimes) && 
               in_array(strtolower($file->getClientOriginalExtension()), $allowedExtensions);
    }

    /**
     * Get optimized image path for different sizes
     * 
     * @param string $imagePath
     * @param string $size (thumbnail, medium, large)
     * @return string
     */
    public static function getOptimizedImageUrl($imagePath, $size = 'medium')
    {
        // For now, return the original image
        // In the future, this could be extended to return different sized versions
        return self::getImageUrl($imagePath);
    }

    /**
     * Copy legacy images from public/images to storage
     * This is a utility function for migration
     * 
     * @param string $sourceDir
     * @param string $targetDir
     * @return array
     */
    public static function migrateLegacyImages($sourceDir = 'public/images', $targetDir = 'storage/app/public')
    {
        $results = [
            'success' => [],
            'errors' => []
        ];

        $sourcePath = base_path($sourceDir);
        $targetPath = base_path($targetDir);

        if (!File::exists($sourcePath)) {
            $results['errors'][] = "Source directory does not exist: {$sourcePath}";
            return $results;
        }

        if (!File::exists($targetPath)) {
            File::makeDirectory($targetPath, 0755, true);
        }

        // This would need to be implemented based on specific migration needs
        // For now, it's a placeholder for future use

        return $results;
    }
}
