# คู่มือการแก้ไขปัญหารูปภาพและการ Deploy

## ปัญหาที่พบบ่อย

### 1. รูปภาพไม่แสดงเมื่อย้ายเว็บไปเครื่องใหม่

**สาเหตุ:** Laravel ใช้ symbolic link เชื่อมโยง `public/storage` กับ `storage/app/public` แต่ symbolic link นี้จะหายไปเมื่อย้ายไฟล์

**วิธีแก้ไข:**
```bash
php artisan storage:link --force
```

### 2. ตรวจสอบสถานะรูปภาพ

ใช้คำสั่งที่เราสร้างขึ้นใหม่:
```bash
# ตรวจสอบปัญหา
php artisan images:check

# ตรวจสอบและแก้ไขอัตโนมัติ
php artisan images:check --fix
```

## ขั้นตอนการ Deploy เว็บไซต์

### 1. การเตรียมเซิร์ฟเวอร์ใหม่

```bash
# 1. Clone โปรเจกต์
git clone [repository-url] project-name
cd project-name

# 2. ติดตั้ง dependencies
composer install --no-dev --optimize-autoloader

# 3. คัดลอกไฟล์ environment
cp .env.example .env

# 4. แก้ไขไฟล์ .env ให้เหมาะสม
# - APP_URL=https://yourdomain.com
# - DB_* settings
# - Other configurations

# 5. สร้าง application key
php artisan key:generate

# 6. รัน migrations
php artisan migrate --force

# 7. สร้าง storage link
php artisan storage:link

# 8. ตั้งค่า permissions (Linux/Mac)
chmod -R 755 storage bootstrap/cache
chmod -R 775 storage/app/public

# 9. ตรวจสอบรูปภาพ
php artisan images:check --fix
```

### 2. การย้ายรูปภาพจากเครื่องเก่า

```bash
# คัดลอกโฟลเดอร์ storage/app/public จากเครื่องเก่า
# ไปยัง storage/app/public ในเครื่องใหม่

# หรือใช้ rsync (Linux/Mac)
rsync -av old-server:/path/to/storage/app/public/ ./storage/app/public/

# หรือใช้ scp
scp -r old-server:/path/to/storage/app/public/* ./storage/app/public/
```

### 3. การตรวจสอบหลัง Deploy

```bash
# ตรวจสอบ storage link
ls -la public/storage

# ตรวจสอบรูปภาพ
php artisan images:check

# ทดสอบการอัปโหลดรูปภาพใหม่
# ไปที่หน้า admin และลองอัปโหลดรูปภาพ
```

## โครงสร้างไฟล์รูปภาพ

```
storage/app/public/
├── menu-items/          # รูปภาพเมนูอาหาร
├── categories/          # รูปภาพหมวดหมู่
├── restaurant/          # รูปภาพร้าน (โลโก้, ปก, พื้นหลัง)
├── about-page/          # รูปภาพหน้าเกี่ยวกับเรา
├── contact-page/        # รูปภาพหน้าติดต่อ
└── gallery/             # รูปภาพทั่วไป

public/images/           # รูปภาพ static (ไอคอน, placeholder)
├── menu/
│   └── placeholder.svg  # รูปภาพ fallback สำหรับเมนู
├── restaurant/
│   └── background.jpg   # รูปพื้นหลังเริ่มต้น
└── logo/
    └── logo.jpg         # โลโก้เริ่มต้น
```

## การแก้ไขปัญหาเฉพาะกรณี

### Windows

```cmd
# สร้าง storage link
php artisan storage:link --force

# ตรวจสอบ
dir public\storage
```

### Linux/Mac

```bash
# สร้าง storage link
php artisan storage:link --force

# ตั้งค่า permissions
chmod -R 755 storage
chmod -R 775 storage/app/public
chown -R www-data:www-data storage (สำหรับ Apache)
chown -R nginx:nginx storage (สำหรับ Nginx)
```

### Docker

```dockerfile
# ในไฟล์ Dockerfile
RUN php artisan storage:link
RUN chmod -R 775 storage/app/public
```

## การใช้งาน ImageHelper

ในไฟล์ Blade template:

```php
<!-- แทนที่ -->
<img src="{{ asset('storage/' . $menu->image) }}" alt="{{ $menu->name }}">

<!-- ด้วย -->
<img src="{{ \App\Helpers\ImageHelper::getMenuImageUrl($menu->image) }}" alt="{{ $menu->name }}">
```

## การตรวจสอบปัญหาด้วยตนเอง

### 1. ตรวจสอบ Storage Link

```bash
# ตรวจสอบว่า public/storage ชี้ไปที่ storage/app/public
ls -la public/storage

# ควรเห็นผลลัพธ์คล้าย:
# lrwxrwxrwx 1 user user 30 date time storage -> ../storage/app/public
```

### 2. ตรวจสอบไฟล์รูปภาพ

```bash
# ตรวจสอบว่ามีไฟล์รูปภาพใน storage
ls -la storage/app/public/menu-items/

# ตรวจสอบว่าเข้าถึงได้ผ่าน public/storage
ls -la public/storage/menu-items/
```

### 3. ทดสอบการเข้าถึงผ่าน URL

เปิดเบราว์เซอร์และไปที่:
```
https://yourdomain.com/storage/menu-items/[filename.jpg]
```

## การ Backup รูปภาพ

### สร้าง Backup

```bash
# สร้าง backup ของรูปภาพทั้งหมด
tar -czf images-backup-$(date +%Y%m%d).tar.gz storage/app/public/

# หรือใช้ zip
zip -r images-backup-$(date +%Y%m%d).zip storage/app/public/
```

### Restore Backup

```bash
# แตกไฟล์ backup
tar -xzf images-backup-20240101.tar.gz

# หรือ
unzip images-backup-20240101.zip

# สร้าง storage link ใหม่
php artisan storage:link --force
```

## การ Monitor รูปภาพ

สร้าง cron job เพื่อตรวจสอบ storage link:

```bash
# เพิ่มใน crontab
0 */6 * * * cd /path/to/project && php artisan images:check --fix > /dev/null 2>&1
```

## การแก้ไขปัญหาเร่งด่วน

หากรูปภาพไม่แสดงทั้งหมด ให้รันคำสั่งนี้:

```bash
# แก้ไขปัญหาทั้งหมดในคำสั่งเดียว
php artisan storage:link --force && php artisan images:check --fix && php artisan cache:clear
```

## การติดต่อขอความช่วยเหลือ

หากยังมีปัญหา ให้ส่งข้อมูลต่อไปนี้:

1. ผลลัพธ์จาก `php artisan images:check`
2. ผลลัพธ์จาก `ls -la public/storage`
3. ข้อความ error จาก browser console
4. ระบบปฏิบัติการที่ใช้
5. เวอร์ชัน PHP และ Laravel
